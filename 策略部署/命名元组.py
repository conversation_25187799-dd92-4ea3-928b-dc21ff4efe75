from collections import namedtuple#命名元组
user=namedtuple('na',['a','b','c'])#['a','b','c']可以用‘a b c'或’a,b,c'等多种表示方式
tbo=user(a=4,b=5,c=6)
print(tbo)
tbo=user._make([7,8,9])
print(tbo)
tbo=user(11,22,33)
print(tbo)
print(tbo.a,tbo.b,tbo.c)

print(id(tbo))
tbo=tbo._replace(a=16,b=25)#内存地址变化，实际为重新划定内存，新建了一个命名元组，原来的元组内存计数归0，会被收回
print(id(tbo))
print(tbo._asdict())
print(tbo)
print(f'a {tbo[0]} ,b {tbo[1]},c {tbo[2]}')#命名元组与tuple完全兼容，可读性，相比字典更加高效和轻量

#把数据库等读出的数据放到命名元组中
from collections import namedtuple
import numpy as np
data=np.arange(12).reshape(4,-1).tolist()
print(data)

tbo=namedtuple('name','a b c')


for data in map(tbo._make,data):
    b=data
    print(b)
