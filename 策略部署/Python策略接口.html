<!DOCTYPE html>
<!-- saved from url=(0054)http://*************:8080/stg/py_stg/py_interface.html -->
<html lang="zh-hans"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        
        
        <title>Python策略接口 · ApolloG2股票交易系统安装及使用手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="刘平">
        
        
    
    <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/style.css">

    
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/website.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/chapter-fold.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/plugin.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/splitter.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/footer.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/plugin(1).css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/website(1).css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/search.css">
                
            
                
                <link rel="stylesheet" href="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/website(2).css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    <meta name="HandheldFriendly" content="true">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="http://*************:8080/gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="http://*************:8080/gitbook/images/favicon.ico" type="image/x-icon">

    
    <link rel="next" href="http://*************:8080/stg/py_stg/py_structure.html">
    
    

    </head>
    <body>
        
<div class="book without-animation with-summary font-size-2 font-family-1">
    <div class="book-summary" style="width: 300px; left: 0px; position: absolute;">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索">
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter" data-level="1.1">
            
                <span>
            
                    
                    概述
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.1.1" data-path="../../">
            
                <a href="http://*************:8080/">
            
                    
                    概述
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.1.2" data-path="../../installation/archetect.html">
            
                <a href="http://*************:8080/installation/archetect.html">
            
                    
                    系统拓扑结构
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.2">
            
                <span>
            
                    
                    系统安装部署
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.2.1" data-path="../../installation/prepare.html">
            
                <a href="http://*************:8080/installation/prepare.html">
            
                    
                    系统部署注意事项
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.2" data-path="../../installation/software_requirement.html">
            
                <a href="http://*************:8080/installation/software_requirement.html">
            
                    
                    软硬件需求
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.3" data-path="../../installation/redis.html">
            
                <a href="http://*************:8080/installation/redis.html">
            
                    
                    Redis数据库部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.4" data-path="../../installation/mysql.html">
            
                <a href="http://*************:8080/installation/mysql.html">
            
                    
                    MySql数据库部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.5" data-path="../../installation/api_gateway.html">
            
                <a href="http://*************:8080/installation/api_gateway.html">
            
                    
                    系统网关部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.6" data-path="../../installation/trading_node.html">
            
                <a href="http://*************:8080/installation/trading_node.html">
            
                    
                    交易节点部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.7" data-path="../../installation/common_service.html">
            
                <a href="http://*************:8080/installation/common_service.html">
            
                    
                    系统服务部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.8" data-path="../../installation/backtest.html">
            
                <a href="http://*************:8080/installation/backtest.html">
            
                    
                    回测系统部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.9" data-path="../../installation/maintenance_tools.html">
            
                <a href="http://*************:8080/installation/maintenance_tools.html">
            
                    
                    运维工具安装部署
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3">
            
                <span>
            
                    
                    配置各组件服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.1">
            
                <span>
            
                    
                    交易网关
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.1.1" data-path="../../components/gw_service.html">
            
                <a href="http://*************:8080/components/gw_service.html">
            
                    
                    交易网关
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.1.2" data-path="../../components/algo_gw.html">
            
                <a href="http://*************:8080/components/algo_gw.html">
            
                    
                    算法交易网关
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.2">
            
                <span>
            
                    
                    行情服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.2.1" data-path="../../components/md_service.html">
            
                <a href="http://*************:8080/components/md_service.html">
            
                    
                    行情服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.2" data-path="../../components/md_playback.html">
            
                <a href="http://*************:8080/components/md_playback.html">
            
                    
                    行情回放服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.3" data-path="../../components/md_monitor.html">
            
                <a href="http://*************:8080/components/md_monitor.html">
            
                    
                    行情监控服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.4" data-path="../../components/md_multicast.html">
            
                <a href="http://*************:8080/components/md_multicast.html">
            
                    
                    组播行情服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.3">
            
                <span>
            
                    
                    交易节点基础服务部署
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.3.1" data-path="../../components/stock_manager.html">
            
                <a href="http://*************:8080/components/stock_manager.html">
            
                    
                    管理服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.2" data-path="../../components/xy_agent.html">
            
                <a href="http://*************:8080/components/xy_agent.html">
            
                    
                    人机代理服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.3" data-path="../../components/log_forward.html">
            
                <a href="http://*************:8080/components/log_forward.html">
            
                    
                    日志转发服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.4" data-path="../../components/log_forward2.html">
            
                <a href="http://*************:8080/components/log_forward2.html">
            
                    
                    新日志转发服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.4">
            
                <span>
            
                    
                    易迅柜台接入相关服务(Remote)
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.4.1" data-path="../../components/yixun.html">
            
                <a href="http://*************:8080/components/yixun.html">
            
                    
                    易迅柜台
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.4.2" data-path="../../components/flow_forward.html">
            
                <a href="http://*************:8080/components/flow_forward.html">
            
                    
                    交易流水转发
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.5">
            
                <span>
            
                    
                    辅助服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.5.1" data-path="../../components/flow_analyzer.html">
            
                <a href="http://*************:8080/components/flow_analyzer.html">
            
                    
                    流水解析服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.6">
            
                <span>
            
                    
                    交易策略
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.6.1" data-path="../../components/stg_gz.html">
            
                <a href="http://*************:8080/components/stg_gz.html">
            
                    
                    股债套利策略
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.6.2" data-path="../../components/stg_algo.html">
            
                <a href="http://*************:8080/components/stg_algo.html">
            
                    
                    算法交易策略
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.4">
            
                <span>
            
                    
                    交易系统API
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.4.1" data-path="../../api/api.html">
            
                <a href="http://*************:8080/api/api.html">
            
                    
                    交易系统Restful API
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.4.2" data-path="../../api/sdk.html">
            
                <a href="http://*************:8080/api/sdk.html">
            
                    
                    交易系统Python SDK
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter expanded" data-level="1.5">
            
                <span>
            
                    
                    策略开发
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter expanded" data-level="1.5.1">
            
                <span>
            
                    
                    Python策略编写说明
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter active expanded" data-level="*******" data-path="py_interface.html">
            
                <a href="http://*************:8080/stg/py_stg/py_interface.html">
            
                    
                    Python策略接口
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="*******" data-path="py_structure.html">
            
                <a href="http://*************:8080/stg/py_stg/py_structure.html">
            
                    
                    Python数据定义
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="*******" data-path="py_demo.html">
            
                <a href="http://*************:8080/stg/py_stg/py_demo.html">
            
                    
                    Python策略示例
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="*******" data-path="py_auto_follow_demo.html">
            
                <a href="http://*************:8080/stg/py_stg/py_auto_follow_demo.html">
            
                    
                    Python策略自动追单示例
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.1.5" data-path="py_build.html">
            
                <a href="http://*************:8080/stg/py_stg/py_build.html">
            
                    
                    Python策略打包签名
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.2">
            
                <span>
            
                    
                    C++策略编写说明
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.2.1" data-path="../cpp_stg/cpp_interface.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_interface.html">
            
                    
                    C++策略接口
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.2.2" data-path="../cpp_stg/cpp_structure.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_structure.html">
            
                    
                    C++数据定义
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.2.3" data-path="../cpp_stg/cpp_demo.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_demo.html">
            
                    
                    C++策略示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.3">
            
                <span>
            
                    
                    策略签名
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.3.1" data-path="../deployment/stg_verification.html">
            
                <a href="http://*************:8080/stg/deployment/stg_verification.html">
            
                    
                    策略签名
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.4">
            
                <span>
            
                    
                    策略回测
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.4.1" data-path="../backtest/backtest_demo.html">
            
                <a href="http://*************:8080/stg/backtest/backtest_demo.html">
            
                    
                    使用回测系统
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.4.2" data-path="../backtest/backtest_faq.html">
            
                <a href="http://*************:8080/stg/backtest/backtest_faq.html">
            
                    
                    回测系统FAQ
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.5">
            
                <span>
            
                    
                    模拟撮合
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.5.1" data-path="../fake_match/fake_match.html">
            
                <a href="http://*************:8080/stg/fake_match/fake_match.html">
            
                    
                    Fake_match撮合规则说明
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.5.2" data-path="../fake_match/sim_match.html">
            
                <a href="http://*************:8080/stg/fake_match/sim_match.html">
            
                    
                    Sim_match撮合规则说明
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.6">
            
                <span>
            
                    
                    策略开发常见问题
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.6.1" data-path="../faq/position.html">
            
                <a href="http://*************:8080/stg/faq/position.html">
            
                    
                    如何配置持仓以及各字段起作用的前提条件
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.6.2" data-path="../faq/stg_risk.html">
            
                <a href="http://*************:8080/stg/faq/stg_risk.html">
            
                    
                    如何配置策略风控
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.6">
            
                <span>
            
                    
                    多活行情
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.6.1" data-path="../../fast_md/deploy.html">
            
                <a href="http://*************:8080/fast_md/deploy.html">
            
                    
                    多活行情部署说明
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.6.2" data-path="../../fast_md/fast_auto_filter.html">
            
                <a href="http://*************:8080/fast_md/fast_auto_filter.html">
            
                    
                    极速行情筛选服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7">
            
                <span>
            
                    
                    性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.1">
            
                <span>
            
                    
                    交易系统穿透性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.1.1" data-path="../../perf/apollo/test_1/test_report_1.html">
            
                <a href="http://*************:8080/perf/apollo/test_1/test_report_1.html">
            
                    
                    第一次测试报告
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.1.2" data-path="../../perf/apollo/test_2/test_report_2.html">
            
                <a href="http://*************:8080/perf/apollo/test_2/test_report_2.html">
            
                    
                    第二次测试报告
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7.2">
            
                <span>
            
                    
                    行情性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.2.1" data-path="../../perf/md/raw_test_1/raw_test_1.html">
            
                <a href="http://*************:8080/perf/md/raw_test_1/raw_test_1.html">
            
                    
                    裸行情性能测试
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.2.2" data-path="../../perf/md/md_service/md_service_test.html">
            
                <a href="http://*************:8080/perf/md/md_service/md_service_test.html">
            
                    
                    行情服务性能测试
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7.3">
            
                <span>
            
                    
                    交易系统整体性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.3.1" data-path="../../perf/apollo/sys_perf/plan.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/plan.html">
            
                    
                    评估方案
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.2" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_20220914_hxnf.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_20220914_hxnf.html">
            
                    
                    华鑫证券南方机房测试报告0914
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.3" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_20220914_dznf.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_20220914_dznf.html">
            
                    
                    东方证券南方机房测试报告0914
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.4" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_dzjq20221212.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_dzjq20221212.html">
            
                    
                    东方证券金桥机房测试报告1212
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.5" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_dznf20221212.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_dznf20221212.html">
            
                    
                    东方证券南方机房测试报告1212
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.8">
            
                <span>
            
                    
                    MFC客户端
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.8.1" data-path="../../mfc/pack_install.html">
            
                <a href="http://*************:8080/mfc/pack_install.html">
            
                    
                    打包和安装规则
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.8.2" data-path="../../mfc/config_path.html">
            
                <a href="http://*************:8080/mfc/config_path.html">
            
                    
                    plugin和provider配置文件路径
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.8.3" data-path="../../mfc/algo_plugin.html">
            
                <a href="http://*************:8080/mfc/algo_plugin.html">
            
                    
                    算法客户端配置
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.9">
            
                <span>
            
                    
                    AM系统
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.9.1" data-path="../../am/am_restful.html">
            
                <a href="http://*************:8080/am/am_restful.html">
            
                    
                    用户接口使用示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.10">
            
                <span>
            
                    
                    FAQ
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.10.1" data-path="../../faq/version_compatibility.html">
            
                <a href="http://*************:8080/faq/version_compatibility.html">
            
                    
                    如何判断交易系统组件版本的兼容
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.2" data-path="../../faq/client_faq.html">
            
                <a href="http://*************:8080/faq/client_faq.html">
            
                    
                    如何解决C#客户端不显示持仓数据
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.3" data-path="../../faq/proto_faq.html">
            
                <a href="http://*************:8080/faq/proto_faq.html">
            
                    
                    如何使用Proto文件解析Redis日志
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.4" data-path="../../faq/xy_error_code.html">
            
                <a href="http://*************:8080/faq/xy_error_code.html">
            
                    
                    系统错误代码定义
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com/" target="blank" class="gitbook-link" style="display: none;">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    <div class="divider-content-summary"><div class="divider-content-summary__icon"><i class="fa fa-ellipsis-v"></i></div></div></div>

    <div class="book-body" style="left: 300px; position: absolute;">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <a class="btn pull-left js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_interface.html#"><i class="fa fa-align-justify"></i></a><div class="dropdown pull-right js-toolbar-action"><a class="btn toggle-dropdown" aria-label="Share" href="http://*************:8080/stg/py_stg/py_interface.html#"><i class="fa fa-share-alt"></i></a><div class="dropdown-menu dropdown-left"><div class="dropdown-caret"><span class="caret-outer"></span><span class="caret-inner"></span></div><div class="buttons"><button class="button size-5 ">Facebook</button><button class="button size-5 ">Google+</button><button class="button size-5 ">Twitter</button><button class="button size-5 ">Weibo</button><button class="button size-5 ">Instapaper</button></div></div></div><a class="btn pull-right js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_interface.html#"><i class="fa fa-facebook"></i></a><a class="btn pull-right js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_interface.html#"><i class="fa fa-twitter"></i></a><div class="dropdown pull-left font-settings js-toolbar-action"><a class="btn toggle-dropdown" aria-label="Font Settings" href="http://*************:8080/stg/py_stg/py_interface.html#"><i class="fa fa-font"></i></a><div class="dropdown-menu dropdown-right"><div class="dropdown-caret"><span class="caret-outer"></span><span class="caret-inner"></span></div><div class="buttons"><button class="button size-2 font-reduce">A</button><button class="button size-2 font-enlarge">A</button></div><div class="buttons"><button class="button size-2 ">Serif</button><button class="button size-2 ">Sans</button></div><div class="buttons"><button class="button size-3 ">White</button><button class="button size-3 ">Sepia</button><button class="button size-3 ">Night</button></div></div></div><h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="http://*************:8080/">Python策略接口</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-navicon"></i><ul><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_interface.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E7%B1%BB"><b>1. </b>Python策略基类</a></li></ul></div><a href="http://*************:8080/stg/py_stg/py_interface.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E7%B1%BB" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><!--
 * Copyright (c) 2016-2021, XYAsset. All rights reserved.
 * 
 * @Author: LiuPing
 * 
 * @Create Date: 2021-07-23 03:33:37
 * 
 * @Last Time: 2022-11-11 14:37:22
 * 
 * @Last Author: Shao Jiaxu
 * 
 * @Description: 
-->
<p>Python策略接口介绍</p>
<h1 id="python策略基类"><a name="python策略基类" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_interface.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E7%B1%BB"><i class="fa fa-link" aria-hidden="true"></i></a>1. Python策略基类</h1>
<p>Python策略基类名称为StrategyInterface，编写Python策略时必须继承该基类。并且其<strong>init</strong>方法必须为如下形式：</p>
<div class="code-wrapper"><pre><code class="lang-python"><span class="code-line">    <span class="hljs-function"><span class="hljs-keyword">def</span> <span class="hljs-title">__init__</span><span class="hljs-params">(self, server)</span>:</span></span>
<span class="code-line">            StrategyInterface.__init__(self, server)</span></code><i class="fa fa-clone t-copy"></i></pre></div>
<p>策略接口中包括主动调用的方法和被动回调的方法，其中被动调用类方法是策略必须实现的方法，主动调用方法是供策略调用的方法。</p>
<p>被动回调方法列表: </p>
<div class="table-area"><table>
<thead>
<tr>
<th>方法</th>
<th>参数</th>
<th>返回值</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>OnInit</td>
<td>无</td>
<td>Bool</td>
<td>策略在实盘初始化时，该方法会被调用</td>
</tr>
<tr>
<td>OnBacktestInit</td>
<td>json 字符串<br>{“trading_day”:20200611}</td>
<td>Bool</td>
<td>策略在回测系统初始化时，该方法会被调用</td>
</tr>
<tr>
<td>OnRelease</td>
<td>无</td>
<td>无</td>
<td>策略退出时，该方法会被调用</td>
</tr>
<tr>
<td>OnMarketData</td>
<td>XyMarketData</td>
<td>无</td>
<td>收到股票行情时，该方法会被调用</td>
</tr>
<tr>
<td>OnTrans</td>
<td>XyTransData</td>
<td>无</td>
<td>收到逐笔成交时，该方法会被调用</td>
</tr>
<tr>
<td>OnOrder</td>
<td>XyOrderData</td>
<td>无</td>
<td>收到逐笔委托时，该方法会被调用</td>
</tr>
<tr>
<td>OnIndex</td>
<td>XyIndexData</td>
<td>无</td>
<td>收到现货指数行情时，该方法会被调用</td>
</tr>
<tr>
<td>OnOrderAccept</td>
<td>RspAcceptedField<br>req_id</td>
<td>无</td>
<td>收到报单接受时，该方法会被调用，req_id是报单或者撤单接口中策略自行传入的id号，下同。</td>
</tr>
<tr>
<td>OnOrderReject</td>
<td>RspRejectedField<br>req_id</td>
<td>无</td>
<td>收到报单拒绝时，该方法会被调用</td>
</tr>
<tr>
<td>OnTrade</td>
<td>RspExecutionField<br>req_id</td>
<td>无</td>
<td>收到成交回报时，该方法会被调用</td>
</tr>
<tr>
<td>OnCancelAccept</td>
<td>RspCancelAcceptedField<br>req_id</td>
<td>无</td>
<td>收到撤单接受时，该方法会被调用</td>
</tr>
<tr>
<td>OnCancelReject</td>
<td>RspCancelRejectedField<br>req_id</td>
<td>无</td>
<td>收到撤单拒绝时，该方法会被调用</td>
</tr>
<tr>
<td>OnTimer</td>
<td>无</td>
<td>无</td>
<td>收到定时器事件</td>
</tr>
<tr>
<td>OnSignal</td>
<td>字节数组</td>
<td>无</td>
<td>策略收到外部推送信号时，该方法会被调用</td>
</tr>
<tr>
<td>NotifyOrderUpdate</td>
<td>ReqStrategyUpdateOrder<br>req_id</td>
<td>无</td>
<td>客户端实时修改订单状态时，该方法会被调用</td>
</tr>
<tr>
<td>NotifyPositionUpdate</td>
<td>ReqStrategyUpdatePosition<br>req_id</td>
<td>无</td>
<td>客户端实时修改策略持仓时，该方法会被调用</td>
</tr>
<tr>
<td>OnCmd</td>
<td>cmd_bytes, cmd_len<br>req_id</td>
<td>rsp_bytes</td>
<td>客户端发起策略命令时, 该方法会被调用</td>
</tr>
<tr>
<td>OnPause</td>
<td>req_id</td>
<td>无</td>
<td>客户端发起暂停指令时, 该方法会被调用</td>
</tr>
<tr>
<td>OnResume</td>
<td>req_id</td>
<td>无</td>
<td>客户端发起恢复指令时, 该方法会被调用</td>
</tr>
<tr>
<td>OnForceClose</td>
<td>req_id</td>
<td>无</td>
<td>客户端发起强平指令时, 该方法会被调用</td>
</tr>
<tr>
<td>OnError</td>
<td>RspErrorField<br>req_id</td>
<td>无</td>
<td>查询出错时, 该方法会被调用</td>
</tr>
<tr>
<td>OnQryPosition</td>
<td>RspAcceptedField<br>req_id</td>
<td>无</td>
<td>收到查询持仓响应</td>
</tr>
<tr>
<td>OnQryCreditPosition</td>
<td>RspQryCreditPositionField<br>RspQryCreditPositionElemField<br>req_id</td>
<td>无</td>
<td>收到查询融券头寸响应</td>
</tr>
<tr>
<td>OnQryFund</td>
<td>RspQryStockFundField<br>req_id</td>
<td>无</td>
<td>收到查询可用资金响应</td>
</tr>
<tr>
<td>OnQryAssetBalance</td>
<td>RspQryAssetBalanceField<br>req_id</td>
<td>无</td>
<td>资产负债查询响应</td>
</tr>
<tr>
<td>OnQryFundFlow</td>
<td>RspQryFundFlowField<br>FundFlowElemField<br>req_id</td>
<td>无</td>
<td>查询资金流水响应</td>
</tr>
<tr>
<td>OnPaybackCash</td>
<td>RspPaybackCashField<br>req_id</td>
<td>无</td>
<td>收到直接还款响应</td>
</tr>
</tbody>
</table></div>
<p>主动调用方法列表: </p>
<div class="table-area"><table>
<thead>
<tr>
<th>方法</th>
<th>参数</th>
<th>返回值</th>
<th>说明</th>
</tr>
</thead>
<tbody>
<tr>
<td>GetLastError</td>
<td>无</td>
<td>最近一次出错的错误码</td>
<td>查询最近一次出错的错误码</td>
</tr>
<tr>
<td>SubStock</td>
<td>股票代码列表<br>订阅类型subtype</td>
<td>错误代码</td>
<td>默认订阅盘口。Subtype=1订阅trans，2订阅order，1+2=3订阅全部</td>
</tr>
<tr>
<td>SubIndex</td>
<td>指数列表</td>
<td>错误代码</td>
<td>订阅现货指数行情</td>
</tr>
<tr>
<td>NewOrder</td>
<td>ReqNewField<br>req_id</td>
<td>local_order_id</td>
<td>报单接口。返回本地订单编号，如果为0则在本地报单失败，之后不会收到报单回报。失败的原因可以通过查询出错代码获得GetLastError。本地报单失败一般是持仓检查失败和风控拦截。Req_id是策略自定义的请求id号，在收到回报的接口中会返回该id，供策略识别。下同。</td>
</tr>
<tr>
<td>CancelOrder</td>
<td>XyOrderId<br>req_id</td>
<td>错误代码</td>
<td>撤单，使用本地报单编号local_order_id撤单（NewOrder接口的返回值）</td>
</tr>
<tr>
<td>GetOrder</td>
<td>XyOrderId</td>
<td>XyStockOrder</td>
<td>获取某只股票的订单信息</td>
</tr>
<tr>
<td>GetAllOrder</td>
<td>无</td>
<td>XyStockOrder list</td>
<td>获取所有订单</td>
</tr>
<tr>
<td>GetPosition</td>
<td>股票代码<br>资金账号</td>
<td>XyStockPosition</td>
<td>获取资金账号下某只股票的持仓信息</td>
</tr>
<tr>
<td>GetAllPosition</td>
<td>无</td>
<td>XyStockPosition list</td>
<td>获取策略所有持仓</td>
</tr>
<tr>
<td>GetAlgoTask</td>
<td>algo_id</td>
<td>XyAlgoTask</td>
<td>获取算法任务信息</td>
</tr>
<tr>
<td>GetAllAlgoTask</td>
<td>无</td>
<td>XyAlgoTask list</td>
<td>获取所有算法任务</td>
</tr>
<tr>
<td>QryFund</td>
<td>ReqQryFundField</td>
<td>bool</td>
<td>查询资金账户的可用资金, 通过 OnQryFund 回调返回资金数据</td>
</tr>
<tr>
<td>QryPosition</td>
<td>ReqQryPositionField</td>
<td>bool</td>
<td>查询资金账户的所有股票持仓, 通过 OnQryPosition 回调返回查询结果</td>
</tr>
<tr>
<td>QryCreditPosition</td>
<td>ReqQryCreditPositionField</td>
<td>bool</td>
<td>查询两融资金账户的股票头寸, 通过 OnQryCreditPosition 回调返回查询结果</td>
</tr>
<tr>
<td>ReqPaybackCash</td>
<td>ReqPaybackCashField</td>
<td>bool</td>
<td>发送直接还款请求, 通过 OnPaybackCash 回调返回查询结果</td>
</tr>
<tr>
<td>QryAssetBalance</td>
<td>ReqQryAssetBalanceField</td>
<td>bool</td>
<td>发送查询资产负债请求, 通过 OnQryAssetBalance 回调返回查询结果</td>
</tr>
<tr>
<td>QryFundFlow</td>
<td>ReqQryFundFlowField</td>
<td>bool</td>
<td>发送资金流水查询请求, 通过 OnQryFundFlow 回调返回查询结果</td>
</tr>
<tr>
<td>LogInfo</td>
<td>日志字符串</td>
<td>无</td>
<td>向交易系统输出信息日志</td>
</tr>
<tr>
<td>LogError</td>
<td>日志字符串</td>
<td>无</td>
<td>向交易系统输出错误日志</td>
</tr>
<tr>
<td>PubInfo</td>
<td>待发布的数据</td>
<td>错误代码</td>
<td>向客户端推送数据</td>
</tr>
<tr>
<td>SetTimer</td>
<td>Interval</td>
<td>True/False</td>
<td>Interval：时间间隔，单位毫秒</td>
</tr>
<tr>
<td>EnableAutoFollow</td>
<td>AutoFollowInitConfig</td>
<td>True/False</td>
<td>自动追单开启接口</td>
</tr>
<tr>
<td>AddAutoFollow</td>
<td>XyOrderId<br>AutoFollowConfig</td>
<td>True/False</td>
<td>添加自动追单</td>
</tr>
</tbody>
</table></div>
<footer class="page-footer"><span class="copyright">Copyright © xyasset.cn 2022 all right reserved，powered by Gitbook</span><span class="footer-modification">该文件修订时间：
2022-11-11 06:41:10
</span></footer>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class="search-results-count"></span> results matching "<span class="search-query"></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class="search-query"></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                
                <a href="http://*************:8080/stg/py_stg/py_structure.html" class="navigation navigation-next navigation-unique" aria-label="Next page: Python数据定义" style="margin-right: 17px;">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Python策略接口","level":"*******","depth":3,"next":{"title":"Python数据定义","level":"*******","depth":3,"path":"stg/py_stg/py_structure.md","ref":"stg/py_stg/py_structure.md","articles":[]},"previous":{"title":"Python策略编写说明","level":"1.5.1","depth":2,"ref":"","articles":[{"title":"Python策略接口","level":"*******","depth":3,"path":"stg/py_stg/py_interface.md","ref":"stg/py_stg/py_interface.md","articles":[]},{"title":"Python数据定义","level":"*******","depth":3,"path":"stg/py_stg/py_structure.md","ref":"stg/py_stg/py_structure.md","articles":[]},{"title":"Python策略示例","level":"*******","depth":3,"path":"stg/py_stg/py_demo.md","ref":"stg/py_stg/py_demo.md","articles":[]},{"title":"Python策略自动追单示例","level":"*******","depth":3,"path":"stg/py_stg/py_auto_follow_demo.md","ref":"stg/py_stg/py_auto_follow_demo.md","articles":[]},{"title":"Python策略打包签名","level":"1.5.1.5","depth":3,"path":"stg/py_stg/py_build.md","ref":"stg/py_stg/py_build.md","articles":[]}]},"dir":"ltr"},"config":{"plugins":["auto-scroll-table","chapter-fold","anchor-navigation-ex","expandable-chapters","splitter","tbfed-pagefooter","code","hide-element"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"Copyright &copy xyasset.cn 2022","modify_label":"该文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"chapter-fold":{},"splitter":{},"search":{},"auto-scroll-table":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"associatedWithSummary":true,"float":{"floatIcon":"fa fa-navicon","level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showGoTop":true,"showLevel":true},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"expandable-chapters":{}},"theme":"default","author":"刘平","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"introduction.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"title":"ApolloG2股票交易系统安装及使用手册","language":"zh-hans","gitbook":"*","theme-default":{"showLevel":true}},"file":{"path":"stg/py_stg/py_interface.md","mtime":"2022-11-11T06:41:10.502Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2023-02-13T02:39:32.584Z"},"basePath":"../..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/gitbook.js.下载"></script>
    <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/theme.js.下载"></script>
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/plugin.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/chapter-fold.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/expandable-chapters.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/splitter.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/plugin.js(1).下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/plugin.js(2).下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/search-engine.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/search.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/lunr.min.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/search-lunr.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/buttons.js.下载"></script>
        
    
        
        <script src="./Python策略接口 · ApolloG2股票交易系统安装及使用手册_files/fontsettings.js.下载"></script>
        
    

    


<textarea id="code-textarea"></textarea></body></html>