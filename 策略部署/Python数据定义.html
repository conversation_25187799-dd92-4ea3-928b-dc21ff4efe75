<!DOCTYPE html>
<!-- saved from url=(0054)http://*************:8080/stg/py_stg/py_structure.html -->
<html lang="zh-hans"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        
        
        <title>Python数据定义 · ApolloG2股票交易系统安装及使用手册</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="description" content="">
        <meta name="generator" content="GitBook 3.2.3">
        <meta name="author" content="刘平">
        
        
    
    <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/style.css">

    
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/website.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/chapter-fold.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/plugin.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/expandable-chapters.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/splitter.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/footer.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/plugin(1).css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/website(1).css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/search.css">
                
            
                
                <link rel="stylesheet" href="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/website(2).css">
                
            
        

    

    
        
    
        
    
        
    
        
    
        
    
        
    

        
    
    
    <meta name="HandheldFriendly" content="true">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="http://*************:8080/gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="http://*************:8080/gitbook/images/favicon.ico" type="image/x-icon">

    
    
    
    
    
    

    <link rel="prev" href="http://*************:8080/stg/py_stg/py_interface.html"><link rel="next" href="http://*************:8080/stg/py_stg/py_demo.html"></head>
    <body>
        
<div class="book without-animation with-summary font-size-2 font-family-1">
    <div class="book-summary" style="width: 300px; left: 0px; position: absolute;">
        
            
<div id="book-search-input" role="search">
    <input type="text" placeholder="输入并搜索">
</div>

            
                <nav role="navigation">
                


<ul class="summary">
    
    

    

    
        
        
    
        <li class="chapter" data-level="1.1">
            
                <span>
            
                    
                    概述
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.1.1" data-path="../../">
            
                <a href="http://*************:8080/">
            
                    
                    概述
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.1.2" data-path="../../installation/archetect.html">
            
                <a href="http://*************:8080/installation/archetect.html">
            
                    
                    系统拓扑结构
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.2">
            
                <span>
            
                    
                    系统安装部署
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.2.1" data-path="../../installation/prepare.html">
            
                <a href="http://*************:8080/installation/prepare.html">
            
                    
                    系统部署注意事项
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.2" data-path="../../installation/software_requirement.html">
            
                <a href="http://*************:8080/installation/software_requirement.html">
            
                    
                    软硬件需求
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.3" data-path="../../installation/redis.html">
            
                <a href="http://*************:8080/installation/redis.html">
            
                    
                    Redis数据库部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.4" data-path="../../installation/mysql.html">
            
                <a href="http://*************:8080/installation/mysql.html">
            
                    
                    MySql数据库部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.5" data-path="../../installation/api_gateway.html">
            
                <a href="http://*************:8080/installation/api_gateway.html">
            
                    
                    系统网关部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.6" data-path="../../installation/trading_node.html">
            
                <a href="http://*************:8080/installation/trading_node.html">
            
                    
                    交易节点部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.7" data-path="../../installation/common_service.html">
            
                <a href="http://*************:8080/installation/common_service.html">
            
                    
                    系统服务部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.8" data-path="../../installation/backtest.html">
            
                <a href="http://*************:8080/installation/backtest.html">
            
                    
                    回测系统部署
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.2.9" data-path="../../installation/maintenance_tools.html">
            
                <a href="http://*************:8080/installation/maintenance_tools.html">
            
                    
                    运维工具安装部署
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3">
            
                <span>
            
                    
                    配置各组件服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.1">
            
                <span>
            
                    
                    交易网关
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.1.1" data-path="../../components/gw_service.html">
            
                <a href="http://*************:8080/components/gw_service.html">
            
                    
                    交易网关
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.1.2" data-path="../../components/algo_gw.html">
            
                <a href="http://*************:8080/components/algo_gw.html">
            
                    
                    算法交易网关
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.2">
            
                <span>
            
                    
                    行情服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.2.1" data-path="../../components/md_service.html">
            
                <a href="http://*************:8080/components/md_service.html">
            
                    
                    行情服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.2" data-path="../../components/md_playback.html">
            
                <a href="http://*************:8080/components/md_playback.html">
            
                    
                    行情回放服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.3" data-path="../../components/md_monitor.html">
            
                <a href="http://*************:8080/components/md_monitor.html">
            
                    
                    行情监控服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.2.4" data-path="../../components/md_multicast.html">
            
                <a href="http://*************:8080/components/md_multicast.html">
            
                    
                    组播行情服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.3">
            
                <span>
            
                    
                    交易节点基础服务部署
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.3.1" data-path="../../components/stock_manager.html">
            
                <a href="http://*************:8080/components/stock_manager.html">
            
                    
                    管理服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.2" data-path="../../components/xy_agent.html">
            
                <a href="http://*************:8080/components/xy_agent.html">
            
                    
                    人机代理服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.3" data-path="../../components/log_forward.html">
            
                <a href="http://*************:8080/components/log_forward.html">
            
                    
                    日志转发服务
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.3.4" data-path="../../components/log_forward2.html">
            
                <a href="http://*************:8080/components/log_forward2.html">
            
                    
                    新日志转发服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.4">
            
                <span>
            
                    
                    易迅柜台接入相关服务(Remote)
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.4.1" data-path="../../components/yixun.html">
            
                <a href="http://*************:8080/components/yixun.html">
            
                    
                    易迅柜台
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.4.2" data-path="../../components/flow_forward.html">
            
                <a href="http://*************:8080/components/flow_forward.html">
            
                    
                    交易流水转发
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.5">
            
                <span>
            
                    
                    辅助服务
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.5.1" data-path="../../components/flow_analyzer.html">
            
                <a href="http://*************:8080/components/flow_analyzer.html">
            
                    
                    流水解析服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.3.6">
            
                <span>
            
                    
                    交易策略
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.3.6.1" data-path="../../components/stg_gz.html">
            
                <a href="http://*************:8080/components/stg_gz.html">
            
                    
                    股债套利策略
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.3.6.2" data-path="../../components/stg_algo.html">
            
                <a href="http://*************:8080/components/stg_algo.html">
            
                    
                    算法交易策略
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.4">
            
                <span>
            
                    
                    交易系统API
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.4.1" data-path="../../api/api.html">
            
                <a href="http://*************:8080/api/api.html">
            
                    
                    交易系统Restful API
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.4.2" data-path="../../api/sdk.html">
            
                <a href="http://*************:8080/api/sdk.html">
            
                    
                    交易系统Python SDK
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter expanded" data-level="1.5">
            
                <span>
            
                    
                    策略开发
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter expanded" data-level="1.5.1">
            
                <span>
            
                    
                    Python策略编写说明
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="*******" data-path="py_interface.html">
            
                <a href="http://*************:8080/stg/py_stg/py_interface.html">
            
                    
                    Python策略接口
            
                </a>
            

            
        </li>
    
        <li class="chapter active expanded" data-level="*******" data-path="py_structure.html">
            
                <a href="http://*************:8080/stg/py_stg/py_structure.html">
            
                    
                    Python数据定义
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="*******" data-path="py_demo.html">
            
                <a href="http://*************:8080/stg/py_stg/py_demo.html">
            
                    
                    Python策略示例
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.1.4" data-path="py_auto_follow_demo.html">
            
                <a href="http://*************:8080/stg/py_stg/py_auto_follow_demo.html">
            
                    
                    Python策略自动追单示例
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.1.5" data-path="py_build.html">
            
                <a href="http://*************:8080/stg/py_stg/py_build.html">
            
                    
                    Python策略打包签名
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.2">
            
                <span>
            
                    
                    C++策略编写说明
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.2.1" data-path="../cpp_stg/cpp_interface.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_interface.html">
            
                    
                    C++策略接口
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.2.2" data-path="../cpp_stg/cpp_structure.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_structure.html">
            
                    
                    C++数据定义
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.2.3" data-path="../cpp_stg/cpp_demo.html">
            
                <a href="http://*************:8080/stg/cpp_stg/cpp_demo.html">
            
                    
                    C++策略示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.3">
            
                <span>
            
                    
                    策略签名
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.3.1" data-path="../deployment/stg_verification.html">
            
                <a href="http://*************:8080/stg/deployment/stg_verification.html">
            
                    
                    策略签名
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.4">
            
                <span>
            
                    
                    策略回测
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.4.1" data-path="../backtest/backtest_demo.html">
            
                <a href="http://*************:8080/stg/backtest/backtest_demo.html">
            
                    
                    使用回测系统
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.4.2" data-path="../backtest/backtest_faq.html">
            
                <a href="http://*************:8080/stg/backtest/backtest_faq.html">
            
                    
                    回测系统FAQ
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.5">
            
                <span>
            
                    
                    模拟撮合
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.5.1" data-path="../fake_match/fake_match.html">
            
                <a href="http://*************:8080/stg/fake_match/fake_match.html">
            
                    
                    Fake_match撮合规则说明
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.5.2" data-path="../fake_match/sim_match.html">
            
                <a href="http://*************:8080/stg/fake_match/sim_match.html">
            
                    
                    Sim_match撮合规则说明
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.5.6">
            
                <span>
            
                    
                    策略开发常见问题
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.5.6.1" data-path="../faq/position.html">
            
                <a href="http://*************:8080/stg/faq/position.html">
            
                    
                    如何配置持仓以及各字段起作用的前提条件
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.5.6.2" data-path="../faq/stg_risk.html">
            
                <a href="http://*************:8080/stg/faq/stg_risk.html">
            
                    
                    如何配置策略风控
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.6">
            
                <span>
            
                    
                    多活行情
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.6.1" data-path="../../fast_md/deploy.html">
            
                <a href="http://*************:8080/fast_md/deploy.html">
            
                    
                    多活行情部署说明
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.6.2" data-path="../../fast_md/fast_auto_filter.html">
            
                <a href="http://*************:8080/fast_md/fast_auto_filter.html">
            
                    
                    极速行情筛选服务
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7">
            
                <span>
            
                    
                    性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.1">
            
                <span>
            
                    
                    交易系统穿透性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.1.1" data-path="../../perf/apollo/test_1/test_report_1.html">
            
                <a href="http://*************:8080/perf/apollo/test_1/test_report_1.html">
            
                    
                    第一次测试报告
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.1.2" data-path="../../perf/apollo/test_2/test_report_2.html">
            
                <a href="http://*************:8080/perf/apollo/test_2/test_report_2.html">
            
                    
                    第二次测试报告
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7.2">
            
                <span>
            
                    
                    行情性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.2.1" data-path="../../perf/md/raw_test_1/raw_test_1.html">
            
                <a href="http://*************:8080/perf/md/raw_test_1/raw_test_1.html">
            
                    
                    裸行情性能测试
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.2.2" data-path="../../perf/md/md_service/md_service_test.html">
            
                <a href="http://*************:8080/perf/md/md_service/md_service_test.html">
            
                    
                    行情服务性能测试
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.7.3">
            
                <span>
            
                    
                    交易系统整体性能评估
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.7.3.1" data-path="../../perf/apollo/sys_perf/plan.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/plan.html">
            
                    
                    评估方案
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.2" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_20220914_hxnf.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_20220914_hxnf.html">
            
                    
                    华鑫证券南方机房测试报告0914
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.3" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_20220914_dznf.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_20220914_dznf.html">
            
                    
                    东方证券南方机房测试报告0914
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.4" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_dzjq20221212.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_dzjq20221212.html">
            
                    
                    东方证券金桥机房测试报告1212
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.7.3.5" data-path="../../perf/apollo/sys_perf/apollo_g2_perf_report_dznf20221212.html">
            
                <a href="http://*************:8080/perf/apollo/sys_perf/apollo_g2_perf_report_dznf20221212.html">
            
                    
                    东方证券南方机房测试报告1212
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.8">
            
                <span>
            
                    
                    MFC客户端
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.8.1" data-path="../../mfc/pack_install.html">
            
                <a href="http://*************:8080/mfc/pack_install.html">
            
                    
                    打包和安装规则
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.8.2" data-path="../../mfc/config_path.html">
            
                <a href="http://*************:8080/mfc/config_path.html">
            
                    
                    plugin和provider配置文件路径
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.8.3" data-path="../../mfc/algo_plugin.html">
            
                <a href="http://*************:8080/mfc/algo_plugin.html">
            
                    
                    算法客户端配置
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.9">
            
                <span>
            
                    
                    AM系统
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.9.1" data-path="../../am/am_restful.html">
            
                <a href="http://*************:8080/am/am_restful.html">
            
                    
                    用户接口使用示例
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    
        <li class="chapter" data-level="1.10">
            
                <span>
            
                    
                    FAQ
            
                <i class="exc-trigger fa"></i></span>
            

            
            <ul class="articles">
                
    
        <li class="chapter" data-level="1.10.1" data-path="../../faq/version_compatibility.html">
            
                <a href="http://*************:8080/faq/version_compatibility.html">
            
                    
                    如何判断交易系统组件版本的兼容
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.2" data-path="../../faq/client_faq.html">
            
                <a href="http://*************:8080/faq/client_faq.html">
            
                    
                    如何解决C#客户端不显示持仓数据
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.3" data-path="../../faq/proto_faq.html">
            
                <a href="http://*************:8080/faq/proto_faq.html">
            
                    
                    如何使用Proto文件解析Redis日志
            
                </a>
            

            
        </li>
    
        <li class="chapter" data-level="1.10.4" data-path="../../faq/xy_error_code.html">
            
                <a href="http://*************:8080/faq/xy_error_code.html">
            
                    
                    系统错误代码定义
            
                </a>
            

            
        </li>
    

            </ul>
            
        </li>
    

    

    <li class="divider"></li>

    <li>
        <a href="https://www.gitbook.com/" target="blank" class="gitbook-link" style="display: none;">
            本书使用 GitBook 发布
        </a>
    </li>
</ul>


                </nav>
            
        
    <div class="divider-content-summary"><div class="divider-content-summary__icon"><i class="fa fa-ellipsis-v"></i></div></div></div>

    <div class="book-body" style="left: 300px; position: absolute;">
        
            <div class="body-inner">
                
                    

<div class="book-header" role="navigation">
    

    <!-- Title -->
    <a class="btn pull-left js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_structure.html#"><i class="fa fa-align-justify"></i></a><div class="dropdown pull-right js-toolbar-action"><a class="btn toggle-dropdown" aria-label="Share" href="http://*************:8080/stg/py_stg/py_structure.html#"><i class="fa fa-share-alt"></i></a><div class="dropdown-menu dropdown-left"><div class="dropdown-caret"><span class="caret-outer"></span><span class="caret-inner"></span></div><div class="buttons"><button class="button size-5 ">Facebook</button><button class="button size-5 ">Google+</button><button class="button size-5 ">Twitter</button><button class="button size-5 ">Weibo</button><button class="button size-5 ">Instapaper</button></div></div></div><a class="btn pull-right js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_structure.html#"><i class="fa fa-facebook"></i></a><a class="btn pull-right js-toolbar-action" aria-label="" href="http://*************:8080/stg/py_stg/py_structure.html#"><i class="fa fa-twitter"></i></a><div class="dropdown pull-left font-settings js-toolbar-action"><a class="btn toggle-dropdown" aria-label="Font Settings" href="http://*************:8080/stg/py_stg/py_structure.html#"><i class="fa fa-font"></i></a><div class="dropdown-menu dropdown-right"><div class="dropdown-caret"><span class="caret-outer"></span><span class="caret-inner"></span></div><div class="buttons"><button class="button size-2 font-reduce">A</button><button class="button size-2 font-enlarge">A</button></div><div class="buttons"><button class="button size-2 ">Serif</button><button class="button size-2 ">Sans</button></div><div class="buttons"><button class="button size-3 ">White</button><button class="button size-3 ">Sepia</button><button class="button size-3 ">Night</button></div></div></div><h1>
        <i class="fa fa-circle-o-notch fa-spin"></i>
        <a href="http://*************:8080/">Python数据定义</a>
    </h1>
</div>




                    <div class="page-wrapper" tabindex="-1" role="main">
                        <div class="page-inner">
                            
<div id="book-search-results">
    <div class="search-noresults">
    
                                <section class="normal markdown-section">
                                
                                <div id="anchor-navigation-ex-navbar"><i class="fa fa-navicon"></i><ul><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E8%AF%B4%E6%98%8E"><b>1. </b>Python策略基本数据类型说明</a></li><ul><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E4%B9%B0%E5%8D%96%E6%96%B9%E5%90%91%E5%AE%9A%E4%B9%89"><b>1.1. </b>买卖方向定义</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%BC%80%E5%B9%B3%E6%96%B9%E5%90%91%E5%AE%9A%E4%B9%89"><b>1.2. </b>开平方向定义</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E7%B1%BB%E5%9E%8B%E5%AE%9A%E4%B9%89"><b>1.3. </b>报单类型定义</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%95%B0%E9%87%8F%E7%B1%BB%E5%9E%8B"><b>1.4. </b>报单数量类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E4%BA%A4%E6%98%93%E6%89%80%E7%9A%84%E7%B1%BB%E5%9E%8B"><b>1.5. </b>交易所的类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%88%90%E4%BA%A4%E6%92%AE%E5%90%88%E7%B1%BB%E5%9E%8B"><b>1.6. </b>成交撮合类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%A7%94%E6%89%98%E6%9B%B4%E6%96%B0%E7%B1%BB%E5%9E%8B"><b>1.7. </b>委托更新类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8C%81%E4%BB%93%E6%9B%B4%E6%96%B0%E7%B1%BB%E5%9E%8B"><b>1.8. </b>持仓更新类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%A7%94%E6%89%98%E7%8A%B6%E6%80%81%E7%B1%BB%E5%9E%8B"><b>1.9. </b>委托状态类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E5%BF%AB%E7%85%A7%E8%A1%8C%E6%83%85"><b>1.10. </b>股票快照行情</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E9%80%90%E7%AC%94%E6%88%90%E4%BA%A4%E8%A1%8C%E6%83%85"><b>1.11. </b>股票逐笔成交行情</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E9%80%90%E7%AC%94%E5%A7%94%E6%89%98%E8%A1%8C%E6%83%85"><b>1.12. </b>股票逐笔委托行情</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E6%8C%87%E6%95%B0%E8%A1%8C%E6%83%85"><b>1.13. </b>股票指数行情</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E8%AF%B7%E6%B1%82"><b>1.14. </b>报单请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8E%A5%E5%8F%97%E5%93%8D%E5%BA%94"><b>1.15. </b>报单接受响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8B%92%E7%BB%9D%E5%93%8D%E5%BA%94"><b>1.16. </b>报单拒绝响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E8%AF%B7%E6%B1%82"><b>1.17. </b>撤单请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E6%8E%A5%E5%8F%97%E5%93%8D%E5%BA%94"><b>1.18. </b>撤单接受响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E6%8B%92%E7%BB%9D%E5%93%8D%E5%BA%94"><b>1.19. </b>撤单拒绝响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%88%90%E4%BA%A4%E5%93%8D%E5%BA%94"><b>1.20. </b>成交响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9B%B4%E6%96%B0%E5%A7%94%E6%89%98%E7%8A%B6%E6%80%81"><b>1.21. </b>更新委托状态</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9B%B4%E6%96%B0%E6%8C%81%E4%BB%93%E7%8A%B6%E6%80%81"><b>1.22. </b>更新持仓状态</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%AE%A2%E5%8D%95%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84"><b>1.23. </b>订单数据结构</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8C%81%E4%BB%93%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84"><b>1.24. </b>持仓数据结构</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B4%A6%E6%88%B7%E6%8C%81%E4%BB%93%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><b>1.25. </b>账户持仓查询请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B4%A6%E6%88%B7%E6%8C%81%E4%BB%93%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><b>1.26. </b>账户持仓查询响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><b>1.27. </b>资产负债查询请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><b>1.28. </b>资产负债查询响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%8F%AF%E7%94%A8%E8%B5%84%E9%87%91%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><b>1.29. </b>可用资金查询请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%8F%AF%E7%94%A8%E8%B5%84%E9%87%91%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><b>1.30. </b>可用资金查询响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E8%B4%A6%E6%88%B7%E8%9E%8D%E5%88%B8%E5%A4%B4%E5%AF%B8%E8%AF%B7%E6%B1%82"><b>1.31. </b>查询账户融券头寸请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E8%B4%A6%E6%88%B7%E8%9E%8D%E5%88%B8%E5%A4%B4%E5%AF%B8%E5%93%8D%E5%BA%94"><b>1.32. </b>查询账户融券头寸响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E9%87%91%E6%B5%81%E6%B0%B4%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><b>1.33. </b>资金流水查询请求</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E9%87%91%E6%B5%81%E6%B0%B4%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94%E5%93%8D%E5%BA%94"><b>1.34. </b>资金流水查询响应响应</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8E%A5%E5%8F%97%E5%9B%9E%E6%8A%A5%E7%9A%84%E5%8F%91%E8%B5%B7%E8%80%85"><b>1.35. </b>报单接受回报的发起者</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E7%AE%97%E6%B3%95%E7%8A%B6%E6%80%81%E7%B1%BB%E5%9E%8B"><b>1.36. </b>算法状态类型</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E5%87%BA%E9%94%99%E5%86%85%E5%AE%B9"><b>1.37. </b>查询出错内容</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E5%B8%82%E5%9C%BA"><b>1.38. </b>市场</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%87%AA%E5%8A%A8%E8%BF%BD%E5%8D%95%E9%85%8D%E7%BD%AE"><b>1.39. </b>自动追单配置</a></li><li><span class="title-icon "></span><a href="http://*************:8080/stg/py_stg/py_structure.html#%E8%87%AA%E5%8A%A8%E8%BF%BD%E5%8D%95%E5%88%9D%E5%A7%8B%E5%8C%96%E9%85%8D%E7%BD%AE"><b>1.40. </b>自动追单初始化配置</a></li></ul></ul></div><a href="http://*************:8080/stg/py_stg/py_structure.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E8%AF%B4%E6%98%8E" id="anchorNavigationExGoTop"><i class="fa fa-arrow-up"></i></a><!--
 * Copyright (c) 2016-2021, XYAsset. All rights reserved.
 * 
 * @Author: LiuPing
 * 
 * @Create Date: 2021-07-23 03:33:37
 * 
 * @Last Time: 2022-11-11 14:36:45
 * 
 * @Last Author: Shao Jiaxu
 * 
 * @Description: 
-->
<h1 id="python策略基本数据类型说明"><a name="python策略基本数据类型说明" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#python%E7%AD%96%E7%95%A5%E5%9F%BA%E6%9C%AC%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B%E8%AF%B4%E6%98%8E"><i class="fa fa-link" aria-hidden="true"></i></a>1. Python策略基本数据类型说明</h1>
<h2 id="买卖方向定义"><a name="买卖方向定义" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E4%B9%B0%E5%8D%96%E6%96%B9%E5%90%91%E5%AE%9A%E4%B9%89"><i class="fa fa-link" aria-hidden="true"></i></a>1.1. 买卖方向定义</h2>
<table>
<thead>
<tr>
<th>Direction</th>
<th>买卖方向</th>
</tr>
</thead>
<tbody>
<tr>
<td>kBuy</td>
<td>买</td>
</tr>
<tr>
<td>kSell</td>
<td>卖</td>
</tr>
<tr>
<td>kFinacingBuy</td>
<td>融资买入</td>
</tr>
<tr>
<td>kFinacingSell</td>
<td>融券卖出</td>
</tr>
<tr>
<td>kBuyBack</td>
<td>买券还券</td>
</tr>
<tr>
<td>kRepayment</td>
<td>卖券还款</td>
</tr>
<tr>
<td>kConvToStock</td>
<td>债转股</td>
</tr>
<tr>
<td>kStockRationed</td>
<td>配股</td>
</tr>
<tr>
<td>kBondRationed</td>
<td>配债</td>
</tr>
<tr>
<td>kUnkonw</td>
<td>未知</td>
</tr>
</tbody>
</table>
<h2 id="开平方向定义"><a name="开平方向定义" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%BC%80%E5%B9%B3%E6%96%B9%E5%90%91%E5%AE%9A%E4%B9%89"><i class="fa fa-link" aria-hidden="true"></i></a>1.2. 开平方向定义</h2>
<table>
<thead>
<tr>
<th>OpenClose</th>
<th>开平方向</th>
</tr>
</thead>
<tbody>
<tr>
<td>kOpen</td>
<td>开仓</td>
</tr>
<tr>
<td>kCloseYesterday</td>
<td>平昨</td>
</tr>
<tr>
<td>kOpenToday</td>
<td>平今</td>
</tr>
<tr>
<td>kClose</td>
<td>平仓</td>
</tr>
</tbody>
</table>
<h2 id="报单类型定义"><a name="报单类型定义" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E7%B1%BB%E5%9E%8B%E5%AE%9A%E4%B9%89"><i class="fa fa-link" aria-hidden="true"></i></a>1.3. 报单类型定义</h2>
<table>
<thead>
<tr>
<th>OrderType</th>
<th>报单类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kLimit</td>
<td>限价</td>
</tr>
<tr>
<td>kFak</td>
<td>Fak</td>
</tr>
<tr>
<td>kFok</td>
<td>Fok</td>
</tr>
</tbody>
</table>
<h2 id="报单数量类型"><a name="报单数量类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%95%B0%E9%87%8F%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.4. 报单数量类型</h2>
<table>
<thead>
<tr>
<th>QtyType</th>
<th>报单数量类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kFixed</td>
<td>指定数量</td>
</tr>
<tr>
<td>kMax</td>
<td>当持仓不足或资金不足时，可以按照当前持仓、资金的最大数量报单</td>
</tr>
</tbody>
</table>
<h2 id="交易所的类型"><a name="交易所的类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E4%BA%A4%E6%98%93%E6%89%80%E7%9A%84%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.5. 交易所的类型</h2>
<table>
<thead>
<tr>
<th>Exchange</th>
<th>交易所的类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kShse</td>
<td>上交所</td>
</tr>
<tr>
<td>kSzse</td>
<td>深交所</td>
</tr>
<tr>
<td>kShfe</td>
<td>上期所</td>
</tr>
<tr>
<td>kCzce</td>
<td>郑商所</td>
</tr>
<tr>
<td>kDce</td>
<td>大商所</td>
</tr>
<tr>
<td>kCffex</td>
<td>中金所</td>
</tr>
<tr>
<td>kIne</td>
<td>能源中心</td>
</tr>
<tr>
<td>kSge</td>
<td>金交所</td>
</tr>
</tbody>
</table>
<h2 id="成交撮合类型"><a name="成交撮合类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%88%90%E4%BA%A4%E6%92%AE%E5%90%88%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.6. 成交撮合类型</h2>
<table>
<thead>
<tr>
<th>MatchingType</th>
<th>成交撮合类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kExchange</td>
<td>交易所撮合</td>
</tr>
<tr>
<td>kSystem</td>
<td>系统撮合</td>
</tr>
</tbody>
</table>
<h2 id="委托更新类型"><a name="委托更新类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%A7%94%E6%89%98%E6%9B%B4%E6%96%B0%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.7. 委托更新类型</h2>
<table>
<thead>
<tr>
<th>OrderUpdateType</th>
<th>委托更新类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kTrade</td>
<td>成交</td>
</tr>
<tr>
<td>kCxl</td>
<td>撤单</td>
</tr>
<tr>
<td>kReject</td>
<td>拒单</td>
</tr>
</tbody>
</table>
<h2 id="持仓更新类型"><a name="持仓更新类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8C%81%E4%BB%93%E6%9B%B4%E6%96%B0%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.8. 持仓更新类型</h2>
<table>
<thead>
<tr>
<th>PositionUpdateType</th>
<th>持仓更新类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kStockTodayBuy</td>
<td>股票今天买入</td>
</tr>
<tr>
<td>kStockBuyFreeze</td>
<td>股票买入冻结</td>
</tr>
<tr>
<td>kStockTodaySell</td>
<td>股票今天卖出</td>
</tr>
<tr>
<td>kStockSellFreeze</td>
<td>股票卖出冻结</td>
</tr>
<tr>
<td>kStockInitPosition</td>
<td>股票初始持仓</td>
</tr>
<tr>
<td>kFutureTLong</td>
<td>期货今多仓</td>
</tr>
<tr>
<td>kFutureYLong</td>
<td>期货昨多仓</td>
</tr>
<tr>
<td>kFutureLongOpening</td>
<td>期货多仓开仓</td>
</tr>
<tr>
<td>kFutureTShortCloseFreeze</td>
<td>期货今空仓平仓冻结</td>
</tr>
<tr>
<td>kFutureYShortCloseFreeze</td>
<td>期货昨空仓平仓冻结</td>
</tr>
<tr>
<td>kFutureTShort</td>
<td>期货今空仓</td>
</tr>
<tr>
<td>kFutureYShort</td>
<td>期货昨空仓</td>
</tr>
<tr>
<td>kFutureShortOpening</td>
<td>期货空仓开仓</td>
</tr>
<tr>
<td>kFutureTLongCloseFreeze</td>
<td>期货今多仓平仓冻结</td>
</tr>
<tr>
<td>kFutureYLongCloseFreeze</td>
<td>期货昨多仓平仓冻结</td>
</tr>
</tbody>
</table>
<h2 id="委托状态类型"><a name="委托状态类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%A7%94%E6%89%98%E7%8A%B6%E6%80%81%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.9. 委托状态类型</h2>
<table>
<thead>
<tr>
<th>OrderStatus</th>
<th>委托状态类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kPendingNew</td>
<td>正报</td>
</tr>
<tr>
<td>kPendingCancel</td>
<td>正撤</td>
</tr>
<tr>
<td>kAccepted</td>
<td>接受</td>
</tr>
<tr>
<td>kRejected</td>
<td>拒绝</td>
</tr>
<tr>
<td>kFilled</td>
<td>成交</td>
</tr>
<tr>
<td>kPartialFilled</td>
<td>部分成交</td>
</tr>
<tr>
<td>kCancelled</td>
<td>撤单</td>
</tr>
<tr>
<td>kPartialCancelled</td>
<td>部分撤单</td>
</tr>
</tbody>
</table>
<h2 id="股票快照行情"><a name="股票快照行情" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E5%BF%AB%E7%85%A7%E8%A1%8C%E6%83%85"><i class="fa fa-link" aria-hidden="true"></i></a>1.10. 股票快照行情</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyMarketData</th>
<th>股票快照行情</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>status</td>
<td>状态</td>
</tr>
<tr>
<td>int</td>
<td>pre_close</td>
<td>前收盘价</td>
</tr>
<tr>
<td>int</td>
<td>open_price</td>
<td>开盘价</td>
</tr>
<tr>
<td>int</td>
<td>high_price</td>
<td>最高价</td>
</tr>
<tr>
<td>int</td>
<td>low_price</td>
<td>最低价</td>
</tr>
<tr>
<td>int</td>
<td>last_price</td>
<td>最新价</td>
</tr>
<tr>
<td>int</td>
<td>high_limited</td>
<td>涨停价</td>
</tr>
<tr>
<td>int</td>
<td>low_limited</td>
<td>跌停价</td>
</tr>
<tr>
<td>int</td>
<td>volume</td>
<td>成交量</td>
</tr>
<tr>
<td>int</td>
<td>turnover</td>
<td>成交额</td>
</tr>
<tr>
<td>int</td>
<td>weighted_avg_bid_price</td>
<td>加权平均委买价格</td>
</tr>
<tr>
<td>int</td>
<td>total_bid_vol</td>
<td>委托买入总量</td>
</tr>
<tr>
<td>int</td>
<td>weighted_avg_ask_price</td>
<td>加权平均委卖价格</td>
</tr>
<tr>
<td>int</td>
<td>total_ask_vol</td>
<td>委托卖出总量</td>
</tr>
<tr>
<td>int数组</td>
<td>ask_price[10]</td>
<td>申卖价</td>
</tr>
<tr>
<td>int数组</td>
<td>bid_price[10]</td>
<td>申买价</td>
</tr>
<tr>
<td>int数组</td>
<td>ask_vol[10]</td>
<td>申卖量</td>
</tr>
<tr>
<td>int数组</td>
<td>bid_vol[10]</td>
<td>申买量</td>
</tr>
<tr>
<td>int</td>
<td>trading_day</td>
<td>交易日</td>
</tr>
<tr>
<td>int</td>
<td>update_time</td>
<td>更新时间</td>
</tr>
<tr>
<td>int</td>
<td>num_trades</td>
<td>成交笔数</td>
</tr>
<tr>
<td>int</td>
<td>iopv</td>
<td>IOPV净值估值</td>
</tr>
<tr>
<td>int</td>
<td>yield_to_maturity</td>
<td>到期收益率</td>
</tr>
<tr>
<td>int</td>
<td>syl1</td>
<td>市盈率1</td>
</tr>
<tr>
<td>int</td>
<td>syl2</td>
<td>市盈率2</td>
</tr>
<tr>
<td>int</td>
<td>sd2</td>
<td>升跌(对比上一笔)</td>
</tr>
<tr>
<td>int</td>
<td>timestamp</td>
<td>极速行情使用的逐笔的last_index</td>
</tr>
</tbody>
</table>
<h2 id="股票逐笔成交行情"><a name="股票逐笔成交行情" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E9%80%90%E7%AC%94%E6%88%90%E4%BA%A4%E8%A1%8C%E6%83%85"><i class="fa fa-link" aria-hidden="true"></i></a>1.11. 股票逐笔成交行情</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyTransData</th>
<th>股票逐笔成交行情</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>code</td>
<td>编码后的股票代码(每个股票固定，不会冲突)</td>
</tr>
<tr>
<td>int</td>
<td>trading_day</td>
<td>交易日</td>
</tr>
<tr>
<td>int</td>
<td>update_time</td>
<td>更新时间</td>
</tr>
<tr>
<td>int</td>
<td>index</td>
<td>成交编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>成交价</td>
</tr>
<tr>
<td>int</td>
<td>volume</td>
<td>成交量</td>
</tr>
<tr>
<td>int</td>
<td>turnover</td>
<td>成交额</td>
</tr>
<tr>
<td>int</td>
<td>bs_flag</td>
<td>买卖方向(bs_flag=32时表示深市撤单)</td>
</tr>
<tr>
<td>int</td>
<td>ask_order</td>
<td>卖方委托编号</td>
</tr>
<tr>
<td>int</td>
<td>bid_order</td>
<td>买方委托编号</td>
</tr>
<tr>
<td>int</td>
<td>channel_no</td>
<td>通道号</td>
</tr>
<tr>
<td>int</td>
<td>market</td>
<td>0表示上交所，1表示深交所</td>
</tr>
<tr>
<td>int</td>
<td>biz_index</td>
<td>逐笔序号，按通道连续</td>
</tr>
<tr>
<td>char</td>
<td>kind</td>
<td>成交类别(已弃用)</td>
</tr>
<tr>
<td>char</td>
<td>function_code</td>
<td>成交代码(已弃用)</td>
</tr>
</tbody>
</table>
<h2 id="股票逐笔委托行情"><a name="股票逐笔委托行情" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E9%80%90%E7%AC%94%E5%A7%94%E6%89%98%E8%A1%8C%E6%83%85"><i class="fa fa-link" aria-hidden="true"></i></a>1.12. 股票逐笔委托行情</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyOrderData</th>
<th>股票逐笔委托行情</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>code</td>
<td>编码后的股票代码(每个股票固定，不会冲突)</td>
</tr>
<tr>
<td>int</td>
<td>trading_day</td>
<td>交易日</td>
</tr>
<tr>
<td>int</td>
<td>update_time</td>
<td>更新时间</td>
</tr>
<tr>
<td>int</td>
<td>order</td>
<td>委托号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>volume</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>channel_no</td>
<td>通道号</td>
</tr>
<tr>
<td>int</td>
<td>market</td>
<td>0表示上交所，1表示深交所</td>
</tr>
<tr>
<td>int</td>
<td>bs_flag</td>
<td>买卖方向(66=买，83=卖)</td>
</tr>
<tr>
<td>int</td>
<td>biz_index</td>
<td>逐笔序号，按通道连续</td>
</tr>
<tr>
<td>char</td>
<td>kind</td>
<td>委托类别(沪市:A=报单，D=撤单，深市:1=市价单，2=限价单，U=本方最优)</td>
</tr>
<tr>
<td>char</td>
<td>function_code</td>
<td>委托代码(已弃用，改用bs_flag)</td>
</tr>
</tbody>
</table>
<h2 id="股票指数行情"><a name="股票指数行情" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%82%A1%E7%A5%A8%E6%8C%87%E6%95%B0%E8%A1%8C%E6%83%85"><i class="fa fa-link" aria-hidden="true"></i></a>1.13. 股票指数行情</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyIndexData</th>
<th>股票指数行情</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>现货指数代码</td>
</tr>
<tr>
<td>int</td>
<td>volume</td>
<td>参与计算相应指数的交易数量</td>
</tr>
<tr>
<td>int</td>
<td>turnover</td>
<td>参与计算相应指数的成交金额</td>
</tr>
<tr>
<td>int</td>
<td>open_index</td>
<td>开盘指数</td>
</tr>
<tr>
<td>int</td>
<td>high_index</td>
<td>最高指数</td>
</tr>
<tr>
<td>int</td>
<td>low_index</td>
<td>最低指数</td>
</tr>
<tr>
<td>int</td>
<td>last_index</td>
<td>最新指数</td>
</tr>
<tr>
<td>int</td>
<td>pre_close_index</td>
<td>前收盘指数</td>
</tr>
<tr>
<td>int</td>
<td>trading_day</td>
<td>交易日</td>
</tr>
<tr>
<td>int</td>
<td>update_time</td>
<td>更新时间</td>
</tr>
</tbody>
</table>
<h2 id="报单请求"><a name="报单请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.14. 报单请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqNewField</th>
<th>报单请求</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托量</td>
</tr>
<tr>
<td>Exchange</td>
<td>exchange</td>
<td>交易所</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
<tr>
<td>OrderType</td>
<td>order_type</td>
<td>委托类型</td>
</tr>
<tr>
<td>QtyType</td>
<td>order_qty_type</td>
<td>数量类型</td>
</tr>
<tr>
<td>int</td>
<td>order_priority</td>
<td>委托优先级</td>
</tr>
</tbody>
</table>
<h2 id="报单接受响应"><a name="报单接受响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8E%A5%E5%8F%97%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.15. 报单接受响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspAcceptedField</th>
<th>报单接受响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>entrust_no</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>accepted_qty</td>
<td>接受数量</td>
</tr>
<tr>
<td>int</td>
<td>remote_order_id</td>
<td>易迅柜台报单编号</td>
</tr>
<tr>
<td>int</td>
<td>accept_time</td>
<td>接受时间</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
</tbody>
</table>
<h2 id="报单拒绝响应"><a name="报单拒绝响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8B%92%E7%BB%9D%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.16. 报单拒绝响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspRejectedField</th>
<th>报单拒绝响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>error_msg</td>
<td>错误信息</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>rejected_qty</td>
<td>拒绝数量</td>
</tr>
<tr>
<td>int</td>
<td>error_code</td>
<td>错误代码</td>
</tr>
<tr>
<td>int</td>
<td>reject_time</td>
<td>拒单时间</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
</tbody>
</table>
<h2 id="撤单请求"><a name="撤单请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.17. 撤单请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqCancelField</th>
<th>撤单请求</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>entrust_no</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>remote_order_id</td>
<td>易迅柜台报单编号</td>
</tr>
<tr>
<td>Exchange</td>
<td>exchange</td>
<td>交易所</td>
</tr>
</tbody>
</table>
<h2 id="撤单接受响应"><a name="撤单接受响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E6%8E%A5%E5%8F%97%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.18. 撤单接受响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspCancelAcceptedField</th>
<th>撤单接受响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>entrust_no</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>cancel_qty</td>
<td>撤单数量</td>
</tr>
<tr>
<td>int</td>
<td>cxl_accept_time</td>
<td>撤单接受时间</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
</tbody>
</table>
<h2 id="撤单拒绝响应"><a name="撤单拒绝响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%92%A4%E5%8D%95%E6%8B%92%E7%BB%9D%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.19. 撤单拒绝响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspCancelRejectedField</th>
<th>撤单拒绝响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>entrust_no</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>string</td>
<td>error_msg</td>
<td>错误信息</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>rejection_qty</td>
<td>撤单拒绝数量</td>
</tr>
<tr>
<td>int</td>
<td>error_code</td>
<td>错误代码</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
</tbody>
</table>
<h2 id="成交响应"><a name="成交响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%88%90%E4%BA%A4%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.20. 成交响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspExecutionField</th>
<th>成交响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账号</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>entrust_no</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>string</td>
<td>trade_id</td>
<td>成交编号</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>trade_price</td>
<td>成交价格</td>
</tr>
<tr>
<td>int</td>
<td>qty</td>
<td>委托数量</td>
</tr>
<tr>
<td>int</td>
<td>trade_qty</td>
<td>成交数量</td>
</tr>
<tr>
<td>int</td>
<td>remote_order_id</td>
<td>易迅柜台报单编号</td>
</tr>
<tr>
<td>int</td>
<td>time</td>
<td>成交时间</td>
</tr>
<tr>
<td>Direction</td>
<td>direction</td>
<td>买卖方向</td>
</tr>
<tr>
<td>OpenClose</td>
<td>open_close</td>
<td>开平方向</td>
</tr>
<tr>
<td>MatchingType</td>
<td>matching_type</td>
<td>撮合类型</td>
</tr>
</tbody>
</table>
<h2 id="更新委托状态"><a name="更新委托状态" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9B%B4%E6%96%B0%E5%A7%94%E6%89%98%E7%8A%B6%E6%80%81"><i class="fa fa-link" aria-hidden="true"></i></a>1.21. 更新委托状态</h2>
<table>
<thead>
<tr>
<th>ReqStrategyUpdateOrder</th>
<th>更新委托状态</th>
</tr>
</thead>
<tbody>
<tr>
<td>request_id</td>
<td>请求ID</td>
</tr>
<tr>
<td>local_order_id</td>
<td>本地报单编号</td>
</tr>
<tr>
<td>update_type</td>
<td>更新类型</td>
</tr>
<tr>
<td>qty</td>
<td>数量</td>
</tr>
<tr>
<td>price</td>
<td>价格</td>
</tr>
</tbody>
</table>
<h2 id="更新持仓状态"><a name="更新持仓状态" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9B%B4%E6%96%B0%E6%8C%81%E4%BB%93%E7%8A%B6%E6%80%81"><i class="fa fa-link" aria-hidden="true"></i></a>1.22. 更新持仓状态</h2>
<table>
<thead>
<tr>
<th>ReqStrategyUpdatePosition</th>
<th>更新持仓状态</th>
</tr>
</thead>
<tbody>
<tr>
<td>request_id</td>
<td>请求ID</td>
</tr>
<tr>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>update_type</td>
<td>更新类型</td>
</tr>
<tr>
<td>qty</td>
<td>数量</td>
</tr>
</tbody>
</table>
<h2 id="订单数据结构"><a name="订单数据结构" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%AE%A2%E5%8D%95%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84"><i class="fa fa-link" aria-hidden="true"></i></a>1.23. 订单数据结构</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyStockOrder</th>
<th>订单数据结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>exchange_order_id</td>
<td>系统报单编号</td>
</tr>
<tr>
<td>int</td>
<td>remote_order_id</td>
<td>易迅柜台报单编号</td>
</tr>
<tr>
<td>int</td>
<td>local_order_id</td>
<td>订单本地报单编号</td>
</tr>
<tr>
<td>int</td>
<td>strategy_id</td>
<td>策略ID</td>
</tr>
<tr>
<td>int</td>
<td>price</td>
<td>委托价格</td>
</tr>
<tr>
<td>int</td>
<td>turnover</td>
<td>成交额</td>
</tr>
<tr>
<td>int</td>
<td>volume</td>
<td>委托量</td>
</tr>
<tr>
<td>int</td>
<td>volume_accept</td>
<td>接受数量</td>
</tr>
<tr>
<td>int</td>
<td>volume_trade</td>
<td>成交数量</td>
</tr>
<tr>
<td>int</td>
<td>volume_cxl</td>
<td>撤单数量</td>
</tr>
<tr>
<td>int</td>
<td>volume_reject</td>
<td>拒绝数量</td>
</tr>
<tr>
<td>int</td>
<td>date</td>
<td>交易日</td>
</tr>
<tr>
<td>int</td>
<td>time_local</td>
<td>本地时间</td>
</tr>
<tr>
<td>int</td>
<td>time_accept</td>
<td>报单接受时间</td>
</tr>
<tr>
<td>int</td>
<td>time_trade</td>
<td>最后成交时间</td>
</tr>
<tr>
<td>int</td>
<td>time_cancel</td>
<td>撤单时间</td>
</tr>
<tr>
<td>OrderStatus</td>
<td>status</td>
<td>状态</td>
</tr>
<tr>
<td>Direction</td>
<td>dir</td>
<td>买卖方向</td>
</tr>
</tbody>
</table>
<h2 id="持仓数据结构"><a name="持仓数据结构" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8C%81%E4%BB%93%E6%95%B0%E6%8D%AE%E7%BB%93%E6%9E%84"><i class="fa fa-link" aria-hidden="true"></i></a>1.24. 持仓数据结构</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>XyStockPosition</th>
<th>持仓数据结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>int</td>
<td>strategy_id</td>
<td>策略ID</td>
</tr>
<tr>
<td>int</td>
<td>initial_position</td>
<td>初始持仓</td>
</tr>
<tr>
<td>int</td>
<td>today_buy</td>
<td>今日买入</td>
</tr>
<tr>
<td>int</td>
<td>freeze_buy</td>
<td>买入冻结</td>
</tr>
<tr>
<td>int</td>
<td>today_sell</td>
<td>今日卖出</td>
</tr>
<tr>
<td>int</td>
<td>freeze_sell</td>
<td>卖出冻结</td>
</tr>
<tr>
<td>int</td>
<td>total_borrow</td>
<td>融券数量</td>
</tr>
<tr>
<td>int</td>
<td>freeze_borrow</td>
<td>融券冻结</td>
</tr>
<tr>
<td>int</td>
<td>max_borrow</td>
<td>融券额度</td>
</tr>
<tr>
<td>int</td>
<td>max_position</td>
<td>最大持仓</td>
</tr>
</tbody>
</table>
<h2 id="账户持仓查询请求"><a name="账户持仓查询请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B4%A6%E6%88%B7%E6%8C%81%E4%BB%93%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.25. 账户持仓查询请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqQryPositionField</th>
<th>账户持仓查询请求结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
</tbody>
</table>
<h2 id="账户持仓查询响应"><a name="账户持仓查询响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B4%A6%E6%88%B7%E6%8C%81%E4%BB%93%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.26. 账户持仓查询响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryPositionField</th>
<th>查询持仓响应信息</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>item_count</td>
<td>明细数量</td>
</tr>
</tbody>
</table>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryPositionElemField</th>
<th>单只股票持仓数据结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>today_long</td>
<td>今买入</td>
</tr>
<tr>
<td>int</td>
<td>today_short</td>
<td>无意义</td>
</tr>
<tr>
<td>int</td>
<td>yesterday_long</td>
<td>可卖数量</td>
</tr>
<tr>
<td>int</td>
<td>yesterday_short</td>
<td>无意义</td>
</tr>
<tr>
<td>double</td>
<td>margin</td>
<td>保证金, 如果值为0表示无法获取该字段</td>
</tr>
<tr>
<td>double</td>
<td>position_profit</td>
<td>浮动盈亏, 如果值为0表示无法获取该字段</td>
</tr>
<tr>
<td>double</td>
<td>position_cost</td>
<td>成本价, 如果值为0表示无法获取该字段</td>
</tr>
</tbody>
</table>
<h2 id="资产负债查询请求"><a name="资产负债查询请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.27. 资产负债查询请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqQryAssetBalanceField</th>
<th>资产负债查询请求</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
</tbody>
</table>
<h2 id="资产负债查询响应"><a name="资产负债查询响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E4%BA%A7%E8%B4%9F%E5%80%BA%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.28. 资产负债查询响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryAssetBalanceField</th>
<th>资产负债查询响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>double</td>
<td>margin_rate</td>
<td>维持担保比例</td>
</tr>
<tr>
<td>int</td>
<td>market_value</td>
<td>市值</td>
</tr>
<tr>
<td>int</td>
<td>total_assert</td>
<td>总资产</td>
</tr>
<tr>
<td>int</td>
<td>total_debts</td>
<td>总负债</td>
</tr>
<tr>
<td>int</td>
<td>margin_value</td>
<td>保证金可用余额</td>
</tr>
<tr>
<td>double</td>
<td>fund_avl</td>
<td>资金可用金额</td>
</tr>
<tr>
<td>double</td>
<td>fund_bln</td>
<td>资金余额</td>
</tr>
<tr>
<td>double</td>
<td>sl_amt</td>
<td>融券卖出所得资金</td>
</tr>
<tr>
<td>double</td>
<td>guarante_out</td>
<td>可转出担保资产</td>
</tr>
<tr>
<td>double</td>
<td>col_mkt_val</td>
<td>担保证券市值</td>
</tr>
<tr>
<td>double</td>
<td>total_fin_fee</td>
<td>融资息费</td>
</tr>
<tr>
<td>double</td>
<td>fi_total_debts</td>
<td>融资负债合计</td>
</tr>
<tr>
<td>double</td>
<td>sl_mkt_val</td>
<td>应付融券市值</td>
</tr>
<tr>
<td>double</td>
<td>total_sl_fee</td>
<td>融券息费</td>
</tr>
<tr>
<td>double</td>
<td>sl_total_debts</td>
<td>融券负债合计</td>
</tr>
<tr>
<td>double</td>
<td>fin_credit</td>
<td>融资授信额度</td>
</tr>
<tr>
<td>double</td>
<td>fin_credit_avl</td>
<td>融资可用额度</td>
</tr>
<tr>
<td>double</td>
<td>sl_credit</td>
<td>融券授信额度</td>
</tr>
<tr>
<td>double</td>
<td>sl_credit_avl</td>
<td>融券可用额度</td>
</tr>
<tr>
<td>double</td>
<td>total_credit</td>
<td>总额度</td>
</tr>
<tr>
<td>double</td>
<td>total_ctedit_avl</td>
<td>总可用额度</td>
</tr>
</tbody>
</table>
<h2 id="可用资金查询请求"><a name="可用资金查询请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%8F%AF%E7%94%A8%E8%B5%84%E9%87%91%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.29. 可用资金查询请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqQryFundField</th>
<th>可用资金查询请求</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
</tbody>
</table>
<h2 id="可用资金查询响应"><a name="可用资金查询响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%8F%AF%E7%94%A8%E8%B5%84%E9%87%91%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.30. 可用资金查询响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryStockFundField</th>
<th>可用资金查询响应</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>double</td>
<td>market_value</td>
<td>证券市值</td>
</tr>
<tr>
<td>double</td>
<td>asset_balance</td>
<td>资产值</td>
</tr>
<tr>
<td>double</td>
<td>fund_pre_bln</td>
<td>期初余额</td>
</tr>
<tr>
<td>double</td>
<td>fund_bln</td>
<td>当前余额</td>
</tr>
<tr>
<td>double</td>
<td>fund_avl</td>
<td>可用资金</td>
</tr>
<tr>
<td>double</td>
<td>fund_frz</td>
<td>冻结资金</td>
</tr>
<tr>
<td>double</td>
<td>fund_ufrz</td>
<td>解冻资金</td>
</tr>
<tr>
<td>double</td>
<td>fund_fetch</td>
<td>可取金额</td>
</tr>
<tr>
<td>double</td>
<td>fund_cash</td>
<td>可取现金</td>
</tr>
</tbody>
</table>
<h2 id="查询账户融券头寸请求"><a name="查询账户融券头寸请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E8%B4%A6%E6%88%B7%E8%9E%8D%E5%88%B8%E5%A4%B4%E5%AF%B8%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.31. 查询账户融券头寸请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqQryCreditPositionField</th>
<th>查询账户融券头寸请求结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
</tbody>
</table>
<h2 id="查询账户融券头寸响应"><a name="查询账户融券头寸响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E8%B4%A6%E6%88%B7%E8%9E%8D%E5%88%B8%E5%A4%B4%E5%AF%B8%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.32. 查询账户融券头寸响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryCreditPositionField</th>
<th>查询账户融券头寸响应信息</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>item_count</td>
<td>明细数量</td>
</tr>
</tbody>
</table>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryCreditPositionElemField</th>
<th>单只股票融券头寸数据结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>symbol</td>
<td>股票代码</td>
</tr>
<tr>
<td>int</td>
<td>asset_total</td>
<td>期初头寸总额度</td>
</tr>
<tr>
<td>int</td>
<td>asset_avl</td>
<td>头寸可用额度</td>
</tr>
<tr>
<td>int</td>
<td>asset_type</td>
<td>头寸类型, 50表示专项融券头寸</td>
</tr>
</tbody>
</table>
<h2 id="资金流水查询请求"><a name="资金流水查询请求" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E9%87%91%E6%B5%81%E6%B0%B4%E6%9F%A5%E8%AF%A2%E8%AF%B7%E6%B1%82"><i class="fa fa-link" aria-hidden="true"></i></a>1.33. 资金流水查询请求</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>ReqQryFundFlowField</th>
<th>资金流水查询请求结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
</tbody>
</table>
<h2 id="资金流水查询响应响应"><a name="资金流水查询响应响应" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%B5%84%E9%87%91%E6%B5%81%E6%B0%B4%E6%9F%A5%E8%AF%A2%E5%93%8D%E5%BA%94%E5%93%8D%E5%BA%94"><i class="fa fa-link" aria-hidden="true"></i></a>1.34. 资金流水查询响应响应</h2>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>RspQryFundFlowField</th>
<th>资金流水查询响应响应信息</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>account</td>
<td>资金账户</td>
</tr>
<tr>
<td>string</td>
<td>item_count</td>
<td>明细数量</td>
</tr>
</tbody>
</table>
<table>
<thead>
<tr>
<th>参数类型</th>
<th>FundFlowElemField</th>
<th>资金流水明细数据结构</th>
</tr>
</thead>
<tbody>
<tr>
<td>string</td>
<td>date</td>
<td>交易日期</td>
</tr>
<tr>
<td>int</td>
<td>time</td>
<td>发生时间</td>
</tr>
<tr>
<td>int</td>
<td>serial_no</td>
<td>流水序号</td>
</tr>
<tr>
<td>int</td>
<td>occur_balance</td>
<td>发生金额</td>
</tr>
<tr>
<td>int</td>
<td>biz_code</td>
<td>业务代码, 1: 入金 2: 出金</td>
</tr>
<tr>
<td>int</td>
<td>trans_status</td>
<td>转账状态, 0: 成功, 1: 失败</td>
</tr>
</tbody>
</table>
<h2 id="报单接受回报的发起者"><a name="报单接受回报的发起者" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%8A%A5%E5%8D%95%E6%8E%A5%E5%8F%97%E5%9B%9E%E6%8A%A5%E7%9A%84%E5%8F%91%E8%B5%B7%E8%80%85"><i class="fa fa-link" aria-hidden="true"></i></a>1.35. 报单接受回报的发起者</h2>
<table>
<thead>
<tr>
<th>AcceptFrom</th>
<th>报单接受回报的发起者</th>
</tr>
</thead>
<tbody>
<tr>
<td>kFromCounter</td>
<td>柜台回报</td>
</tr>
<tr>
<td>kFromExchange</td>
<td>交易所回报</td>
</tr>
<tr>
<td>kFromAlg</td>
<td>算法交易回报</td>
</tr>
</tbody>
</table>
<h2 id="算法状态类型"><a name="算法状态类型" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E7%AE%97%E6%B3%95%E7%8A%B6%E6%80%81%E7%B1%BB%E5%9E%8B"><i class="fa fa-link" aria-hidden="true"></i></a>1.36. 算法状态类型</h2>
<table>
<thead>
<tr>
<th>AlgoTaskStatus</th>
<th>算法状态类型</th>
</tr>
</thead>
<tbody>
<tr>
<td>kAlgoPreparing</td>
<td>正在报单</td>
</tr>
<tr>
<td>kAlgoWaiting</td>
<td>等待状态</td>
</tr>
<tr>
<td>kAlgoRunning</td>
<td>正在运行</td>
</tr>
<tr>
<td>kAlgoSuspend</td>
<td>任务暂停</td>
</tr>
<tr>
<td>kAlgoEnding</td>
<td>最后阶段</td>
</tr>
<tr>
<td>kAlgoFinished</td>
<td>任务完成</td>
</tr>
<tr>
<td>kAlgoCanceled</td>
<td>任务撤销</td>
</tr>
<tr>
<td>kAlgoRejected</td>
<td>任务拒绝</td>
</tr>
</tbody>
</table>
<h2 id="查询出错内容"><a name="查询出错内容" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E6%9F%A5%E8%AF%A2%E5%87%BA%E9%94%99%E5%86%85%E5%AE%B9"><i class="fa fa-link" aria-hidden="true"></i></a>1.37. 查询出错内容</h2>
<table>
<thead>
<tr>
<th>RspErrorField</th>
<th>查询出错内容</th>
</tr>
</thead>
<tbody>
<tr>
<td>type</td>
<td>查询类型</td>
</tr>
<tr>
<td>error_code</td>
<td>错误代码</td>
</tr>
<tr>
<td>error_msg</td>
<td>错误内容</td>
</tr>
</tbody>
</table>
<h2 id="市场"><a name="市场" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E5%B8%82%E5%9C%BA"><i class="fa fa-link" aria-hidden="true"></i></a>1.38. 市场</h2>
<table>
<thead>
<tr>
<th style="text-align:left">StockMarket</th>
<th style="text-align:left">股票市场</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">kMarketA</td>
<td style="text-align:left">A股(包括创业版)</td>
</tr>
<tr>
<td style="text-align:left">kMarketKc</td>
<td style="text-align:left">科创版</td>
</tr>
<tr>
<td style="text-align:left">kMarketBond</td>
<td style="text-align:left">债券</td>
</tr>
</tbody>
</table>
<h2 id="自动追单配置"><a name="自动追单配置" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%87%AA%E5%8A%A8%E8%BF%BD%E5%8D%95%E9%85%8D%E7%BD%AE"><i class="fa fa-link" aria-hidden="true"></i></a>1.39. 自动追单配置</h2>
<table>
<thead>
<tr>
<th style="text-align:left">参数类型</th>
<th style="text-align:left">AutoFollowConfig</th>
<th style="text-align:left">自动追单配置</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">int</td>
<td style="text-align:left">max_times</td>
<td style="text-align:left">最大追单次数</td>
</tr>
<tr>
<td style="text-align:left">int</td>
<td style="text-align:left">tick_cnt</td>
<td style="text-align:left">(按时间)追单加价数量tick_cnt个最小变动单位</td>
</tr>
<tr>
<td style="text-align:left">int</td>
<td style="text-align:left">tick_cnt_snap</td>
<td style="text-align:left">(按盘口)追单加价数量tick_cnt个最小变动单位</td>
</tr>
<tr>
<td style="text-align:left">StockMarket</td>
<td style="text-align:left">market</td>
<td style="text-align:left">股票市场</td>
</tr>
<tr>
<td style="text-align:left">float</td>
<td style="text-align:left">add_ratio</td>
<td style="text-align:left">(按时间)追单加价比例</td>
</tr>
<tr>
<td style="text-align:left">float</td>
<td style="text-align:left">add_ratio_snap</td>
<td style="text-align:left">(盘口)追单加价比例</td>
</tr>
</tbody>
</table>
<h2 id="自动追单初始化配置"><a name="自动追单初始化配置" class="anchor-navigation-ex-anchor" href="http://*************:8080/stg/py_stg/py_structure.html#%E8%87%AA%E5%8A%A8%E8%BF%BD%E5%8D%95%E5%88%9D%E5%A7%8B%E5%8C%96%E9%85%8D%E7%BD%AE"><i class="fa fa-link" aria-hidden="true"></i></a>1.40. 自动追单初始化配置</h2>
<table>
<thead>
<tr>
<th style="text-align:left">参数类型</th>
<th style="text-align:left">AutoFollowInitConfig</th>
<th style="text-align:left">自动追单配置</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">int</td>
<td style="text-align:left">interval</td>
<td style="text-align:left">追单时间间隔(毫秒)</td>
</tr>
<tr>
<td style="text-align:left">int</td>
<td style="text-align:left">version</td>
<td style="text-align:left">版本, 可填1或2</td>
</tr>
</tbody>
</table>
<footer class="page-footer"><span class="copyright">Copyright © xyasset.cn 2022 all right reserved，powered by Gitbook</span><span class="footer-modification">该文件修订时间：
2022-11-11 06:41:10
</span></footer>
                                
                                </section>
                            
    </div>
    <div class="search-results">
        <div class="has-results">
            
            <h1 class="search-results-title"><span class="search-results-count"></span> results matching "<span class="search-query"></span>"</h1>
            <ul class="search-results-list"></ul>
            
        </div>
        <div class="no-results">
            
            <h1 class="search-results-title">No results matching "<span class="search-query"></span>"</h1>
            
        </div>
    </div>
</div>

                        </div>
                    </div>
                
            </div>

            
                
                <a href="http://*************:8080/stg/py_stg/py_interface.html" class="navigation navigation-prev " aria-label="Previous page: Python策略接口">
                    <i class="fa fa-angle-left"></i>
                </a>
                
                
                <a href="http://*************:8080/stg/py_stg/py_demo.html" class="navigation navigation-next " aria-label="Next page: Python策略示例" style="margin-right: 17px;">
                    <i class="fa fa-angle-right"></i>
                </a>
                
            
        
    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"Python数据定义","level":"*******","depth":3,"next":{"title":"Python策略示例","level":"*******","depth":3,"path":"stg/py_stg/py_demo.md","ref":"stg/py_stg/py_demo.md","articles":[]},"previous":{"title":"Python策略接口","level":"*******","depth":3,"path":"stg/py_stg/py_interface.md","ref":"stg/py_stg/py_interface.md","articles":[]},"dir":"ltr"},"config":{"plugins":["auto-scroll-table","chapter-fold","anchor-navigation-ex","expandable-chapters","splitter","tbfed-pagefooter","code","hide-element"],"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"pluginsConfig":{"tbfed-pagefooter":{"copyright":"Copyright &copy xyasset.cn 2022","modify_label":"该文件修订时间：","modify_format":"YYYY-MM-DD HH:mm:ss"},"chapter-fold":{},"splitter":{},"search":{},"auto-scroll-table":{},"lunr":{"maxIndexSize":1000000,"ignoreSpecialCharacters":false},"code":{"copyButtons":true},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"highlight":{},"anchor-navigation-ex":{"associatedWithSummary":true,"float":{"floatIcon":"fa fa-navicon","level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"mode":"float","multipleH1":true,"pageTop":{"level1Icon":"","level2Icon":"","level3Icon":"","showLevelIcon":false},"printLog":false,"showGoTop":true,"showLevel":true},"sharing":{"facebook":true,"twitter":true,"google":false,"weibo":false,"instapaper":false,"vk":false,"all":["facebook","google","twitter","weibo","instapaper"]},"theme-default":{"styles":{"website":"styles/website.css","pdf":"styles/pdf.css","epub":"styles/epub.css","mobi":"styles/mobi.css","ebook":"styles/ebook.css","print":"styles/print.css"},"showLevel":false},"expandable-chapters":{}},"theme":"default","author":"刘平","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"introduction.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{},"title":"ApolloG2股票交易系统安装及使用手册","language":"zh-hans","gitbook":"*","theme-default":{"showLevel":true}},"file":{"path":"stg/py_stg/py_structure.md","mtime":"2022-11-11T06:41:10.503Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2023-02-13T02:39:32.584Z"},"basePath":"../..","book":{"language":""}});
        });
    </script>
</div>

        
    <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/gitbook.js.下载"></script>
    <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/theme.js.下载"></script>
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/plugin.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/chapter-fold.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/expandable-chapters.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/splitter.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/plugin.js(1).下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/plugin.js(2).下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/search-engine.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/search.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/lunr.min.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/search-lunr.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/buttons.js.下载"></script>
        
    
        
        <script src="./Python数据定义 · ApolloG2股票交易系统安装及使用手册_files/fontsettings.js.下载"></script>
        
    

    


<textarea id="code-textarea"></textarea></body></html>