point = 0.00001
import numpy as np

def getEffectivePrice(priceList):

    i = 1
    while i<= len(priceList):
        if priceList[-i] > point:
            return priceList[-i]#如果第十档价格大于0，返回第十档价格
        i+=1
    return priceList[0]

def getAskPriceLimit(askPrice,lastAskPrice,width):

    maxAsk = getEffectivePrice(askPrice) #取当前卖盘盘口最大值
    maxLastAsk = getEffectivePrice(lastAskPrice)#取上一个tick卖盘盘口最大值

    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)#当前卖一和上一个tick卖一相比，最大值加宽度

def getBidPriceLimit(bidPrice,lastBidPrice,width):

    minBid = getEffectivePrice(bidPrice)
    minLastBid =getEffectivePrice(lastBidPrice)

    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price,askPrice,askVolume):#取小于等于指定价格的所有成交量之和

    volumeBelow = 0
    for i in range(0,len(askPrice)):

        if askPrice[i] < price + point and askPrice[i] > point:

            volumeBelow += askVolume[i]

    return volumeBelow

def volumeUpPrice(price,bidPrice,bidVolume):

    volumeUp = 0
    for i in range(0, len(bidPrice)):

        if bidPrice[i] > price - point and bidPrice[i] > point:
            volumeUp += bidVolume[i]

    return volumeUp

def threeSecondMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):
    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)#上一个tick于当前tick比较出来的卖价最小值
    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)#上一个tick于当前tick比较出来的买价最大值

    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)#上一个tick小于等于指定价格的所有成交量之和（卖盘）
    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)#当前tick小于等于指定价格的所有成交量之和

    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)#上一个tick大于等于指定价格的所有成交量之和（买盘）
    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)#当前tick大于等于指定价格的所有成交量之和

    deltaAsk = lastVolumeBelow - volumeBelow#上tick的卖量之和减当前tick卖量之和
    deltaBid = volumeUp - lastVolumeUp#上tick的买量之和减当前tick买量之和

    return deltaAsk + deltaBid

def longResist(askPrice,askVolume,width):

    resist = 0

    for i in range(0,10):

        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:#如果当前卖盘某一党小于卖一价+宽度 and 当前买盘价格大于0

            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):

    resist = 0
    for i in range(0, 10):

        if bidPrice[i] > bidPrice[0] - width - point:
            resist += bidVolume[i]

    return resist

class plateMovementResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.movementList = [0]

        self.longMovementSignalList = []
        self.shortMovementSignalList = []

    def getRationalWidth(self):

        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01

    def getResist(self):

        for i in range(0, len(self.askPriceList)):
            self.longResistList.append(longResist(self.askPriceList[i], self.askVolumeList[i], self.width))#提取卖盘宽度内的所有成交量
            self.shortResistList.append(shortResist(self.bidPriceList[i], self.bidVolumeList[i], self.width))#提取买盘内的所有成交量

    def getMovement(self):

        for i in range(1, len(self.askPriceList)):

            movement = threeSecondMovement(self.askPriceList[i - 1], self.bidPriceList[i - 1], self.askVolumeList[i - 1], self.bidVolumeList[i - 1],
                self.askPriceList[i], self.bidPriceList[i], self.askVolumeList[i], self.bidVolumeList[i],self.width)#计算当前买卖盘动量之和
            if movement > point:
                self.movementList.append(movement/self.longResistList[i])
            elif movement < - point:
                self.movementList.append(movement / self.shortResistList[i])
            else:
                self.movementList.append(0)

    def getMovementSignal(self,ratioLow,ratioHigh):

        for i in range(0, len(self.movementList)):

            if self.movementList[i] >= ratioLow and self.movementList[i] <= ratioHigh :

                self.longMovementSignalList.append(1)
            else:
                self.longMovementSignalList.append(0)

            if -self.movementList[i] >= ratioLow and -self.movementList[i] <=ratioHigh :
                self.shortMovementSignalList.append(1)
            else:
                self.shortMovementSignalList.append(0)






