# -*- coding: utf-8 -*-
import singleStockPlateMovement as ssp
import pandas as pd


processNumber = 40

def getStockList():

    dataFrame = pd.read_csv("stockList_YU.csv", encoding='gbk')
    stockCodeList = []
    stockNameList = []

    for i in range(0,len(dataFrame)):

        stockCodeList.append(dataFrame.iloc[i,0])
        stockNameList.append(dataFrame.iloc[i,1])

    return stockCodeList,stockNameList

def basketStockTest(startDate,endDate,startTime, endTime,platePercent,
                    ratioLow,ratioHigh,tWapDuration,signalDuration):
    stockCodeList,stockNameList = getStockList()
    ret_list = []
    for i in range(len(stockCodeList)):
        print(stockCodeList[i])
        ret_list.append((ssp.getTotalTest(
        startDate,endDate,stockCodeList[i],startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration)))

    meanLongMovementList = [g[0] for g in ret_list]
    lengthLongMovementList = [g[1] for g in ret_list]
    meanShortMovementList = [g[2] for g in ret_list]
    lengthShortMovementList = [g[3] for g in ret_list]


    movementLong = 0
    movementShort = 0
    revertLong = 0
    revertShort = 0
    for i in range(0, len(meanLongMovementList)):
        movementLong += meanLongMovementList[i] * lengthLongMovementList[i]
        movementShort += meanShortMovementList[i] * lengthShortMovementList[i]

    print(movementLong / sum(lengthLongMovementList), movementShort / sum(lengthShortMovementList))
    print(sum(lengthLongMovementList), sum(lengthShortMovementList))

    result = pd.DataFrame({"stockCode": stockCodeList, "stockName":stockNameList,
                           "多头":meanLongMovementList,"多头样本量":lengthLongMovementList,"空头":meanShortMovementList,"空头样本量":lengthShortMovementList,
                           },
                          index=None)
    str = 'plateMovement.csv'
    result.to_csv(str, encoding='gbk', )

if __name__ == '__main__':

    startTime =93100
    endTime = 145000

    ratioHigh = 10000.0
    ratioLow= 4.0

    platePercent = 0.002

    tWapDuration = 60
    signalDuration = 60

    startDate = 20190614
    endDate = 20190618

    basketStockTest(startDate, endDate, startTime, endTime, platePercent,
                    ratioLow,ratioHigh, tWapDuration, signalDuration)