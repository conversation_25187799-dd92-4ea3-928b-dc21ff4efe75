# -*- coding: utf-8 -*-
"""
Created on Mon Aug 27 13:15:37 2018

@author: liuyang
"""

import struct
#import numpy as np
import pandas as pd
import os


typedic = {}
typedic['BarData'] = '3iq6i2qi5q'
typedic['Tick'] = 'q3i2qiqiq4i'
typedic['Tick5'] = 'q3i2qiqiqiqiqiqiqiqiqiqiq4i'
typedic['MktInfo'] = 'qiiid'
typedic['ZJZ'] = 'if'
typedic['TICKAB']='=32s32s3i2i2q2c2Q4i10i10I10i10I2i2Q5i'
typedic['Dalian']='11i2Qd2q54i'
typedic['index']='=32s32siiiiiiiqqi'
typedic['order']='32s32siiiiicc'
typedic['transHC']='=32s32s3iccc4i'
typedic['trans']='=32s32s3iIiIiccii'

typedic['market']='32s32s4iIIIII10I10I10I10IIqqqqIIiiII4siii'
typedic['marketHC']='32s32s4iIIIII10I10I10I10IIqqqqIIiiII4siii2s'
typedic['orderqueue']='32s32s6i200i'
typedic['FTick5'] = '6iq25i'

#typedic['DCETAQ'] = '12iqqdqq54i'
#typedic['DCEOS'] = 'iiqiiqii'
typedic['DCETAQ'] = '10iqqdqq54i'
#typedic['DCEOS'] = 'iiqiiqii'
typedic['DCEOS'] = 'iiqqqq'

def read_dat(dat, dat_type):
    struct_fmt = typedic[dat_type]
    struct_len = struct.calcsize(struct_fmt)
    #print(struct_len)
    struct_unpack = struct.Struct(struct_fmt).unpack_from
    dat_size = len(dat)
    data_list = []
    try:
        for i in range(0,dat_size,struct_len):
            line = dat[i: (i + struct_len)]
            s = struct_unpack(line)
            data_list.append(s)
    except Exception as e:
        print(e)
        pass
        #print(fn, 'not Found')
    return data_list


def get_tdlist(BeginT,EndT):
    filename = 'F:/tyb/Sam/PYTHON/HF/tdlist.csv'
    all_list = pd.read_csv(filename,header=None,names=['td'])
    tdlist = all_list[(all_list['td'] >= BeginT) & (all_list['td'] <= EndT)].iloc[:,0].tolist()
    return tdlist

def get_symlist(exch='ALL'):
    filename = 'F:/tyb/Sam/PYTHON/HF/future_info.csv'
    symdf = pd.read_csv(filename,header=0)
    if exch=='ALL':
        symlist=symdf['sym'].tolist()
    else:
        symlist=symdf[symdf['EXCHANGE']==exch]['sym'].tolist()
    #tdlist = all_list[(all_list['td'] >= BeginT) & (all_list['td'] <= EndT)].iloc[:,0].tolist()
    return symlist 

def get_pcdict(BeginT, EndT, sym_list):    
    pcdict={}
    mldir = 'M:/'    
    for sym in sym_list:

        mlfn = mldir+sym+'888_pctr.csv'
        mldf = pd.read_csv(mlfn,encoding='gbk',header=None)
#        print(mldf)
        ml_list = mldf[(mldf[3]>=BeginT) & (mldf[3]<=EndT)].iloc[:,[2,3]].values.tolist()
        #首个交易日的调整
        sym_firstdate = mldf.iloc[0,5]
        sym_firstctr = mldf.iloc[0,2]
        if BeginT<=sym_firstdate:
            ml_list.insert(0,[sym_firstctr, sym_firstdate])
#            print(ml_list)        
        pcdict[sym] = {x[1]:x[0] for x in ml_list}
    return pcdict


def dat_tolist(fn,dat_type):
    struct_fmt = typedic[dat_type]
    struct_len = struct.calcsize(struct_fmt)
    #print(struct_len)
    struct_unpack = struct.Struct(struct_fmt).unpack_from
    
    data_list = []
    try:
        with open(fn , "rb") as f:
            while True:
                data = f.read(struct_len)
                if not data: 
                    break
                s = struct_unpack(data)
                data_list.append(s)
    except FileNotFoundError as e:
        print(e)
        pass
        #print(fn, 'not Found')
    return data_list

def dat_to_csv(fn,dat_type):
    #tmp_path = r'D:\test'
    tl = dat_tolist(fn,dat_type)
    df=pd.DataFrame(tl)
    #tmp_fout = os.path.join(tmp_path, fout)
    #df.to_csv(tmp_fout,index=False, header=False)
    return df

def get_dir(fn_dir):
    path = os.listdir(fn_dir)
    
   # for i in path:
     #   print(os.listdir(fn_dir + '\\' + i))
    path1 = os.path.join(fn_dir, path[0])
   # if os.path.isdir(path1)
    print(os.path.join(fn_dir, path[0]))
    print(os.path.isdir(path1))
    print(path)


                
                                
if __name__=='__main__':
    """
    tdlist = get_tdlist(20151221,20190116)
    for td in tdlist:
        fn = 'S:/Index/'+str(td)+'/000300.SH.dat'
        AAA=dat_tolist(fn, 'index')
        if len(AAA)<=1000:
            print(td)
    """
#    fn = 'E:/FutureData/Stock_L2/trans/20200803/000001.SZ.dat'
#    fn = '\\/192.168.2.9/hfshare/hc_stock_data/data/TickAB_add/20161107/000001.dat'
    #fn_dir = r'\\192.168.2.12\FutureData\Stock_L2\market'
    
   
    
    #fn = 'Z:/BarData/j888/86400/j888_86400.dat'
#    fn='S:/Trans/20181128/600959.SH.dat'
#    fn='S:/Index/20190619/000300.SH.dat'
#    fn='Z:/Tick/IC888/IC888_20200203.dat'
#    fn='Z:/Tick/20000001/20000001_20190809.dat'
    #fn='Z:/Bardata/MA888/86400/MA888_86400.dat'
    #fn='D:/Cdata/DCE_L2_dat/a888/a888_20140102.dat'
    #fn='D:/Cdata/download/DCE_daily/20181107/TAQ/J/J1901/20181107.dat'
    #fn='D:/Cdata/download/DCE_daily/20181105/ORERS/A/A1901/20181105.dat'
    #fn='Z:/Bardata/20000001/60/20000001_20181203.dat'
    #fn='E:/FutureData/CZCE_L2_dat_new/SR888/SR888_20181102.dat'
#    AAA=dat_tolist(fn,'market')

	# L2数据转化例子
    fn = 'Z:/Market/20190125/000001.SZ.dat'
    dat_to_csv(fn,'market')
    
