# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import signalEstimateTwap as set
import plateMovement as pm
import numpy as np
import filedata

def getDailyTest(path_csv,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration):
    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)
    print(askPriceList)
    print('1')
    plateMovementResearch = pm.plateMovementResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercent)
    
    
    plateMovementResearch.getRationalWidth()#计算宽度
    plateMovementResearch.getResist()#提取买卖盘宽度内的成交量
    plateMovementResearch.getMovement()
    plateMovementResearch.getMovementSignal(ratioLow,ratioHigh)

    longMovementSignalList = plateMovementResearch.longMovementSignalList
    shortMovementSignalList = plateMovementResearch.shortMovementSignalList

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    profitListLongMovement = signalEstimate.longSignalEstimateTwap(signalDuration, longMovementSignalList)
    profitListShortMovement = signalEstimate.shortSignalEstimateTwap(signalDuration, shortMovementSignalList)

    return profitListLongMovement, profitListShortMovement

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration):

    file_list=filedata.path_name(startDate,endDate,stockCode)
    totalProfitListLongMovement = []
    totalProfitListShortMovement = []

    for path_csv in file_list:
        try:
            profitListLongMovement, profitListShortMovement = getDailyTest(path_csv,startTime, endTime,platePercent,ratioLow,ratioHigh,tWapDuration,signalDuration)
        except:
            continue
        else:
            for n in profitListLongMovement:
                totalProfitListLongMovement.append(n)
            for n in profitListShortMovement:
                totalProfitListShortMovement.append(n)


    if len(totalProfitListLongMovement) ==0:
        meanLongMovement = 0
    else:
        meanLongMovement = np.mean(totalProfitListLongMovement)

    if len(totalProfitListShortMovement) ==0:
        meanShortMovement = 0
    else:
        meanShortMovement = np.mean(totalProfitListShortMovement)

    return meanLongMovement,len(totalProfitListLongMovement),meanShortMovement,len(totalProfitListShortMovement)

if __name__ == '__main__':

    stockCode = "000001.SZ"

    startTime = 93100
    endTime = 145000

    #动量值得范围
    ratioHigh = 10000.0
    ratioLow = 4.0

    #用于计算有效挂单的宽度
    platePercent = 0.002

    # 业绩比较基准，多少秒的tWap
    tWapDuration = 120

    # 最小采样间隔，单位是秒
    signalDuration = 60

    # 回测的开始结束日期
    startDate = 20190614
    endDate = 20190614

    meanLongMovement, lengthLongMovement, meanShortMovement, lengthShortMovement = getTotalTest(startDate, endDate, stockCode, startTime, endTime,platePercent,
                                                                ratioLow,ratioHigh,tWapDuration, signalDuration)
    print(meanLongMovement, lengthLongMovement, meanShortMovement, lengthShortMovement)

