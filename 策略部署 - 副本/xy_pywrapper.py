
from collections import namedtuple


class StrategyInterface:
    def __init__(self,server):
        pass


    def SubStock(self,list,number):
        return 0


    def LogError(self,content):
        print(content)
        raise ('订阅失败')

    def LogInfo(self,content):
        a=5
        print(content)

    def GetOrder(self,a):
        '''获取股票信息'''
        pass

    def CancelOrder(self,a,b):
        pass

    def ReqNewField(self):
        return namedtuple('User', ['account'])


class ReqNewField:
    def __init__(self):
        pass




