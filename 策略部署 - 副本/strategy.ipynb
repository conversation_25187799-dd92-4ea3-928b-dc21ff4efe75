{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b1a1e537", "metadata": {}, "outputs": [], "source": ["# -*- coding: utf-8 -*-\n", "import struct\n", "from xy_pywrapper import *\n", "import numpy as np\n", "point = 0.000001"]}, {"cell_type": "code", "execution_count": 2, "id": "a2066a13", "metadata": {}, "outputs": [], "source": ["def getTime(time):\n", "\n", "    time = str(int(time))\n", "    milliSecond = int(time[-3:])\n", "    second = int(time[-5:-3])\n", "    minute = int(time[-7:-5])\n", "    hour = int(time[:-7])\n", "\n", "    return milliSecond,second,minute,hour"]}, {"cell_type": "markdown", "id": "6a16b5a1", "metadata": {}, "source": ["**param time:      int 145037520   \n", "return:          tuple(520,37,50,14)   \n", "时间拆成毫秒，秒，分钟，小时组成的元组**"]}, {"cell_type": "code", "execution_count": 5, "id": "b3e5e476", "metadata": {}, "outputs": [], "source": ["def timeGap(startTime,endTime):\n", "    milliSecondStart, secondStart, minuteStart, hourStart = getTime(startTime)\n", "    milliSecondEnd, secondEnd, minuteEnd, hourEnd = getTime(endTime)\n", "\n", "\n", "    milliSecondBreak = 0\n", "    secondBreak = 10\n", "    minuteBreak = 30\n", "    hourBreak = 11\n", "\n", "    milliSecondRestart = 0\n", "    secondRestart = 50\n", "    minuteRestart = 59\n", "    hourRestart = 12\n", "\n", "    gap1 =(hourBreak -hourStart)*3600 +(minuteBreak-minuteStart)*60 + (secondBreak-secondStart) + 0.001*(milliSecondBreak-milliSecondStart)\n", "\n", "    gap2 = (hourEnd-hourRestart) * 3600 + (minuteEnd-minuteRestart) * 60 + (secondEnd-secondRestart) + 0.001*(milliSecondEnd-milliSecondRestart)\n", "\n", "    if gap1 > -point and gap2 > -point:\n", "\n", "        return 0.25 + max(0,(11 -hourStart)*3600 +(30-minuteStart)*60 + (0-secondStart) + 0.001*(0-milliSecondStart))\n", "\n", "    else:\n", "        gap = (hourEnd -hourStart)*3600 +(minuteEnd-minuteStart)*60 + (secondEnd-secondStart) + 0.001*(milliSecondEnd-milliSecondStart)\n", "\n", "    return gap\n"]}, {"cell_type": "markdown", "id": "53355304", "metadata": {}, "source": ["**param startTime: int 93000000  \n", "param endTime: int 100000000  \n", "return: 1800单位秒**\n"]}, {"cell_type": "code", "execution_count": 6, "id": "b3bdfd8a", "metadata": {}, "outputs": [], "source": ["def longResist(askPrice,askVolume,width):\n", "    resist = 0\n", "\n", "    for i in range(0,10):\n", "\n", "        if askPrice[i] <= askPrice[0] + width and askPrice[i] >0:\n", "\n", "            resist += askVolume[i]\n", "\n", "    return resist"]}, {"cell_type": "markdown", "id": "f38b6084", "metadata": {}, "source": ["**param askPrice: 卖一到卖十价  \n", "param askVolume: 卖一到卖十量  \n", "param width: 宽度，即计算盘口量的有效价格范围  \n", "return: 返回该tick的有效价格范围内的卖方盘口量之和**\n", "\n"]}, {"cell_type": "code", "execution_count": 7, "id": "6fae80e9", "metadata": {}, "outputs": [], "source": ["def shortResist(bidPrice,bidVolume,width):\n", "    resist = 0\n", "    for i in range(0, 10):\n", "\n", "        if bidPrice[i] >= bidPrice[0] - width:\n", "            resist += bidVolume[i]\n", "\n", "    return resist"]}, {"cell_type": "code", "execution_count": 8, "id": "b35c3569", "metadata": {}, "outputs": [], "source": ["def getEffectivePrice(priceList):\n", "    i = 1\n", "    while i<= len(priceList):\n", "        if priceList[-i] > 0:\n", "            return priceList[-i]\n", "        i+=1\n", "    return priceList[0]"]}, {"cell_type": "markdown", "id": "e890a2cc", "metadata": {}, "source": ["**param priceList: 十档价位  \n", "return: 不为0的最高档位的价格**\n"]}, {"cell_type": "code", "execution_count": 9, "id": "597a884d", "metadata": {}, "outputs": [], "source": ["def getAskPriceLimit(askPrice,lastAskPrice,width):\n", "\n", "    maxAsk = getEffectivePrice(askPrice)\n", "    maxLastAsk = getEffectivePrice(lastAskPrice)\n", "\n", "    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)"]}, {"cell_type": "code", "execution_count": 10, "id": "c841b060", "metadata": {}, "outputs": [], "source": ["def getBidPriceLimit(bidPrice,lastBidPrice,width):\n", "\n", "    minBid = getEffectivePrice(bidPrice)\n", "    minLastBid =getEffectivePrice(lastBidPrice)\n", "\n", "    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)"]}, {"cell_type": "markdown", "id": "6df977b1", "metadata": {}, "source": ["**计算有效价格范围，计算有效范围的盘口单量**"]}, {"cell_type": "code", "execution_count": 11, "id": "5d0b7d88", "metadata": {}, "outputs": [], "source": ["def volumeBelowPrice(price,askPrice,askVolume):\n", "\n", "    volumeBelow = 0\n", "    for i in range(0,len(askPrice)):\n", "\n", "        if askPrice[i] <= price and askPrice[i] > 0:\n", "\n", "            volumeBelow += askVolume[i]\n", "\n", "    return volumeBelow\n", "\n", "def volumeUpPrice(price,bidPrice,bidVolume):\n", "\n", "    volumeUp = 0\n", "    for i in range(0, len(bidPrice)):\n", "\n", "        if bidPrice[i] >= price and bidPrice[i] > 0:\n", "            volumeUp += bidVolume[i]\n", "\n", "    return volumeUp\n"]}, {"cell_type": "markdown", "id": "aaa3bdd7", "metadata": {}, "source": ["**有效价格范围内，盘口量**"]}, {"cell_type": "code", "execution_count": 13, "id": "add74445", "metadata": {}, "outputs": [], "source": ["def tickMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):\n", "\n", "    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)\n", "    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)\n", "\n", "    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)\n", "    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)\n", "\n", "    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)\n", "    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)\n", "\n", "    deltaAsk = lastVolumeBelow - volumeBelow\n", "    deltaBid = volumeUp - lastVolumeUp\n", "\n", "    return deltaAsk + deltaBid"]}, {"cell_type": "markdown", "id": "4e60fef7", "metadata": {}, "source": ["### **相比上一个tick,计算买方增加量，计算卖方减少量，叠加作为看多的依据**"]}, {"cell_type": "markdown", "id": "27b52055", "metadata": {}, "source": ["# --------------------------------------------------------------------------------\n", "# 盘口因子，盘口压单，三秒动量\n", "## 各种因子最终落在每条tick上的信号为，1，0，-1，分别指向开多，不操作，开空\n", "## update方法接收行情推送过来的marketData对象，里面包含tick信息\n", "## 该类被实例化之后，持续接收每条tick信息的marketData对象，并更新类的属性，实时生成该tick的信号结果\n"]}, {"cell_type": "code", "execution_count": 14, "id": "88a1b1a7", "metadata": {}, "outputs": [], "source": ["\n", "class plateFactor:\n", "\n", "    def __init__(self,platePercent,timeGap):\n", "\n", "        self.askPrice = np.zeros(10)\n", "        self.bidPrice = np.zeros(10)\n", "        self.askVolume = np.zeros(10)\n", "        self.bidVolume = np.zeros(10)\n", "\n", "        self.lastAskPrice = self.askPrice\n", "        self.lastBidPrice = self.bidPrice\n", "        self.lastAskVolume = self.askVolume\n", "        self.lastBidVolume = self.bidVolume\n", "\n", "        self.timeGap = timeGap\n", "        self.lastUpdateTime = 0\n", "\n", "        self.platePercent = platePercent\n", "        self.width = 0\n", "\n", "        self.longResist = 0\n", "        self.shortResist = 0\n", "\n", "        self.sizeRatio = 1\n", "        self.movement = 0\n", "        self.slip = 100\n", "\n", "        self.firstMarketData = 1\n", "\n", "    def update(self,marketData):\n", "\n", "        if self.firstMarketData:\n", "\n", "            if timeGap(93000000,marketData.update_time) > 3.0:\n", "                #首条tick,且在开盘3秒之后\n", "                #maketData应该是封装好的类对象，类属性对应tick数据的各项值\n", "\n", "                self.askPrice = marketData.ask_price\n", "                self.bidPrice = marketData.bid_price\n", "                self.askVolume = marketData.ask_vol\n", "                self.bidVolume = marketData.bid_vol\n", "\n", "                self.lastUpdateTime = marketData.update_time\n", "\n", "                price = (self.askPrice[0] + self.bidPrice[0]) * 0.5\n", "                self.width = np.ceil(price * self.platePercent / 100.0) * 100.0\n", "                self.firstMarketData = 0\n", "\n", "        else:\n", "\n", "            self.askPrice = marketData.ask_price\n", "            self.bidPrice = marketData.bid_price\n", "            self.askVolume = marketData.ask_vol\n", "            self.bidVolume = marketData.bid_vol\n", "\n", "            if timeGap(self.lastUpdateTime,marketData.update_time) > self.timeGap:\n", "\n", "                self.longResist = longResist(self.askPrice, self.askVolume, self.width)#卖盘口量\n", "                self.shortResist = shortResist(self.bidPrice, self.bidVolume, self.width)#买盘口量\n", "\n", "                self.sizeRatio = float(self.shortResist)/self.longResist#买量比上卖量\n", "                movement = tickMovement(self.lastAskPrice, self.lastBidPrice, self.lastAskVolume,self.lastBidVolume,\n", "                                self.askPrice, self.bidPrice, self.askVolume,self.bidVolume, self.width)#买增量与卖减量之和\n", "                if movement > point:\n", "                    self.movement = float(movement) / self.longResist\n", "                elif movement < - point:\n", "                    self.movement = float(movement) / self.shortResist\n", "                else:\n", "                    self.movement = 0\n", "                #self.timeGap时间间隔外的movement挂单量值变化，正为向上，负为向下\n", "\n", "                self.slip =float(self.askPrice[0]-self.bidPrice[0])/(self.askPrice[0]+self.bidPrice[0])*10000.0#滑点项\n", "\n", "                self.lastUpdateTime = marketData.update_time#记录上一条tick的数据\n", "                self.lastAskPrice = self.askPrice\n", "                self.lastBidPrice = self.bidPrice\n", "                self.lastAskVolume = self.askVolume\n", "                self.lastBidVolume = self.bidVolume\n"]}, {"cell_type": "markdown", "id": "4869c510", "metadata": {}, "source": ["# --------------------------------------------------------------------------------\n", "# 空间因子 波动率 高低点\n", "## 最终落在每条tick上的信号为，1，0，-1，分别指向开多，不操作，开空\n", "## update方法接收行情推送过来的marketData对象，里面包含tick信息\n", "## 该类被实例化之后，持续接收每条tick信息的marketData对象，并更新类的属性，实时生成该tick的信号结果"]}, {"cell_type": "code", "execution_count": 15, "id": "b4f51ee1", "metadata": {}, "outputs": [], "source": ["class spaceFactor:#空间因子\n", "\n", "    def __init__(self,spacePercent,timeGap):\n", "\n", "        self.askPrice = np.zeros(10)\n", "        self.bidPrice = np.zeros(10)\n", "        self.askVolume = np.zeros(10)\n", "        self.bidVolume = np.zeros(10)\n", "\n", "        self.spacePercent = spacePercent\n", "        self.width = 0\n", "\n", "        self.timeGap = timeGap\n", "        self.lastUpdateTime = 0\n", "\n", "        self.space = 0\n", "        self.price = 0\n", "        self.duration = 0\n", "        self.lastDuration = 0\n", "        self.highLowPoint = 0\n", "        self.spaceDuration = 0\n", "\n", "        self.maxPrice = 0\n", "        self.minPrice = 0\n", "\n", "        self.firstMarketData = 1\n", "\n", "    def update(self,marketData):\n", "\n", "        if self.firstMarketData:\n", "\n", "            if timeGap(93000000,marketData.update_time) > 3.0:\n", "\n", "                self.askPrice = marketData.ask_price\n", "                self.bidPrice = marketData.bid_price\n", "                self.askVolume = marketData.ask_vol\n", "                self.bidVolume = marketData.bid_vol\n", "\n", "                self.lastUpdateTime = marketData.update_time\n", "\n", "                price = (self.askPrice[0] + self.bidPrice[0]) * 0.5\n", "                self.width = np.ceil(price * self.spacePercent / 100.0) * 100.0\n", "                self.firstMarketData = 0\n", "\n", "                self.maxPrice = marketData.high_price\n", "                self.minPrice = marketData.low_price\n", "                self.price = price\n", "\n", "        else:\n", "\n", "            self.askPrice = marketData.ask_price\n", "            self.bidPrice = marketData.bid_price\n", "            self.askVolume = marketData.ask_vol\n", "            self.bidVolume = marketData.bid_vol\n", "\n", "            if timeGap(self.lastUpdateTime, marketData.update_time) > self.timeGap:\n", "\n", "                if self.bidPrice[0] - self.price >= self.width:\n", "\n", "                    if self.space <= 0:\n", "                        self.space = 1\n", "                    else:\n", "                        self.space += 1\n", "\n", "                    self.price = self.bidPrice[0]\n", "                    self.lastDuration = self.duration\n", "                    self.duration = 0\n", "\n", "                    if self.price > self.maxPrice + point:\n", "                        self.maxPrice = self.price\n", "                        self.highLowPoint = 1\n", "\n", "                    else:\n", "                        self.highLowPoint = 0\n", "\n", "                elif self.price - self.askPrice[0] >= self.width:\n", "\n", "                    if self.space >= 0:\n", "                        self.space = -1\n", "                    else:\n", "                        self.space -= 1\n", "\n", "                    self.price = self.askPrice[0]\n", "                    self.lastDuration = self.duration\n", "                    self.duration = 0\n", "\n", "                    if self.price < self.minPrice - point:\n", "                        self.minPrice = self.price\n", "                        self.highLowPoint = -1\n", "                    else:\n", "                        self.highLowPoint = 0\n", "                else:\n", "                    self.duration += timeGap(self.lastUpdateTime, marketData.update_time)\n", "\n", "                self.lastUpdateTime = marketData.update_time\n", "                self.spaceDuration = max(self.duration,self.lastDuration)#对应每条tick[20,20,20,14,14,14,14,21，21，21，21]"]}, {"cell_type": "markdown", "id": "a9842b21", "metadata": {}, "source": ["### 传入各种因子的阈值开平仓参数，合并多种因子，计算合并后的信号，最终返回-1、0、1\n", "## 盘口压单，3秒动量，波动率，空间因子，高低点，滑点共6种因子"]}, {"cell_type": "code", "execution_count": 18, "id": "e0e0c150", "metadata": {}, "outputs": [], "source": ["class signalCompute:\n", "    #合并多因子计算信号\n", "\n", "    def __init__(self, minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort):\n", "\n", "        self.minSizeRatio = minSizeRatio\n", "        self.minMovement = minMovement\n", "        self.maxDuration = maxDuration\n", "        self.maxSlipLong = maxSlipLong\n", "        self.maxSizeRatio = maxSizeRatio\n", "        self.maxMovement = maxMovement\n", "        self.maxSlipShort = maxSlipShort\n", "        self.signal = 0\n", "\n", "    def update(self,sizeRatio,movement,slip,space,spaceDuration,highLowPoint):\n", "\n", "        if sizeRatio > self.minSizeRatio - point  and movement > self.minMovement - point and spaceDuration < self.maxDuration + point \\\n", "                and space >= 2 and highLowPoint == 1 and slip < self.maxSlipLong + point:\n", "            self.signal = 1\n", "        elif sizeRatio <self.maxSizeRatio + point and movement < self.maxMovement + point and space <= -1 and\\\n", "                slip < self.maxSlipShort + point:\n", "            self.signal = -1\n", "        else:\n", "            self.signal = 0"]}, {"cell_type": "markdown", "id": "e5ea571a", "metadata": {}, "source": ["# 实例化空间和盘口相关的2个类  \n", "## 收到每条tick信息对象marketData，\n", "## 调用空间类的方法 update,返回该tick对应的3个因子返回结果\n", "## 调用盘口类的方法 update,返回该tick对应的3个因子返回结果\n", "## 实例化信号计算类，调用update方法，传入该tick的6个因子返回结果并产生最终信号-1，0，1   \n", "###  \n", "\n", "<font color=blue size=4>\n", "到这里为止，系统推送过来每条tick的marketData对象,实例化汇总类，实例化空间类，盘口类，实例化计算类，最终得到策略结果1_开多，-1_开空或者0_什么都不做。  \n", "marketData对象是系统主动推过来的，可使用调用方法等多种方式实现,对于策略来说是被动接收，对于系统来说是主动推送。 \n", "</font>"]}, {"cell_type": "code", "execution_count": 21, "id": "a7204e4e", "metadata": {}, "outputs": [], "source": ["class marketDataToSignal:\n", "\n", "    def __init__(self,minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort,\n", "                 spacePercent,spaceTimeGap,platePercent,plateTimeGap):\n", "\n", "        self.signalCompute = signalCompute(minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort)\n", "        self.space = spaceFactor(spacePercent,spaceTimeGap)\n", "        self.plate = plateFactor(platePercent,plateTimeGap)\n", "        self.signal = 0\n", "\n", "    def update(self,marketData):\n", "\n", "        self.space.update(marketData)\n", "        space = self.space.space\n", "        spaceDuration = self.space.spaceDuration\n", "        highLowPoint = self.space.highLowPoint\n", "\n", "        self.plate.update(marketData)\n", "        sizeRatio = self.plate.sizeRatio\n", "        movement = self.plate.movement\n", "        slip = self.plate.slip\n", "\n", "        self.signalCompute.update(sizeRatio,movement,slip,space,spaceDuration,highLowPoint)\n", "        self.signal = self.signalCompute.signal"]}, {"cell_type": "markdown", "id": "ce01b5a5", "metadata": {}, "source": ["# 处理报撤单的类\n", "## 在因子类计算出信号后，需要考虑持仓，可用仓位，是否有挂单等\n", "## 策略接入系统，写法应该是大致固定的模式，很多方法实现应该是通用的，可反复使用。  \n", "## 要做好规范性，尤其是命名规范性\n"]}, {"cell_type": "code", "execution_count": 23, "id": "d98ce404", "metadata": {}, "outputs": [], "source": ["\n", "class signalToOrder:\n", "\n", "    def __init__(self,maxNetPosition,maxVolume,stockType,warningLimit,startTime,endTime,quantLimit,openLimit,stopLimit,limitOrderDuration,maxDrawDownRatio):\n", "\n", "        #开始交易时间\n", "        self.startTime = startTime\n", "        #清仓时间\n", "        self.endTime = endTime\n", "\n", "        #累积接收的有效行情数量\n", "        self.quantOfMarketData = 0\n", "        #开仓前需要至少累积多少笔行情\n", "        self.quantLimit =quantLimit\n", "        #用于计算逼近涨停的价格\n", "        self.openLimit = openLimit\n", "        #逼近涨停价格\n", "        self.openLimitPrice = 0\n", "        #用于计算逼近跌停的价格\n", "        self.stopLimit = stopLimit\n", "        #逼近跌停价格\n", "        self.stopLimitPrice = 0\n", "        #用于计算是否达到止损条件\n", "        self.maxDrawDownRatio = maxDrawDownRatio\n", "        #挂单超时判定\n", "        self.limitOrderDuration = limitOrderDuration\n", "\n", "        #记录当前挂着的多头单，已经成交了多少\n", "        self.totalTradeQuantLong = 0\n", "        #记录当前挂着的空头单，已经成交了多少\n", "        self.totalTradeQuantShort = 0\n", "\n", "        #最大净持仓\n", "        self.maxNetPosition = maxNetPosition\n", "        #最大可用底仓\n", "        self.maxVolume = maxVolume\n", "        #净持仓\n", "        self.netPosition = 0\n", "        #总成交额 ???\n", "        self.totalVolume = 0\n", "\n", "        #开始交易标记\n", "        self.startTrading = 0\n", "        #停止交易，开始清仓标记\n", "        self.stopTrading = 0\n", "\n", "        #stockType=0为非科创板，stockType=1为科创板\n", "        self.stockType =stockType\n", "\n", "        #用于记录多头挂单信息\n", "        self.longPendingOrder = 0\n", "        self.longLimitPrice = 0\n", "        self.longLimitVolume = 0\n", "        self.longOrderTime = 0\n", "\n", "        #用于记录多头正报单信息\n", "        self.longSendingOrder = 0\n", "        self.longSendPrice = 0\n", "        self.longSendVolume = 0\n", "\n", "        #空头挂单信息\n", "        self.shortPendingOrder = 0\n", "        self.shortLimitPrice = 0\n", "        self.shortLimitVolume = 0\n", "        self.shortOrderTime = 0\n", "\n", "        #空头正报单信息\n", "        self.shortSendingOrder = 0\n", "        self.shortSendPrice = 0\n", "        self.shortSendVolume = 0\n", "\n", "        #多头单编号，空头单编号\n", "        self.longOrderId = 0\n", "        self.shortOrderId = 0\n", "\n", "        #连续警告次数记录，警告门线\n", "        self.warning = 0\n", "        self.warningLimit = warningLimit\n", "\n", "        #记录开仓价格，用于计算止损\n", "        self.openPrice = 0\n", "\n", "    #计算当前可平仓位\n", "    def getVolumeShort(self):\n", "\n", "        #非创业板\n", "        if self.stockType ==0:\n", "            #净持仓的零股部分\n", "            return int(self.netPosition/100.0)*100\n", "        #创业板\n", "        else:\n", "            #小于200，无仓可平\n", "            if self.netPosition < 200:\n", "                return 0\n", "            #否则，全部净持仓可平\n", "            else:\n", "                return self.netPosition\n", "\n", "    #计算当前可开仓位\n", "    def get<PERSON><PERSON><PERSON><PERSON><PERSON>(self):\n", "\n", "        #计算目标净持仓，等于最大净持仓和可用底仓取小\n", "        targetPosition = min(self.maxNetPosition , self.maxVolume - self.totalVolume)\n", "\n", "        #非创业板\n", "        if self.stockType == 0:\n", "            #目标持仓减去当前净持仓取整\n", "            return int((targetPosition - self.netPosition) / 100.0) * 100\n", "        #创业板\n", "        else:\n", "            if targetPosition - self.netPosition < 200:\n", "                return 0\n", "            else:\n", "                return targetPosition - self.netPosition\n", "\n", "    #逼近跌停的价格计算\n", "    def getStopLimitPrice(self,marketData):\n", "\n", "        self.stopLimitPrice = int((self.stopLimit * marketData.low_limited + (1-self.stopLimit) * marketData.high_limited)/100.0)*100\n", "\n", "    #逼近涨停的价格计算\n", "    def getOpenLimitPrice(self, marketData):\n", "        self.openLimitPrice = int(\n", "            (self.openLimit * marketData.low_limited + (1 - self.openLimit) * marketData.high_limited) / 100.0) * 100\n", "\n", "    #止损标记计算\n", "    def getStopFlag(self,marketData):\n", "\n", "        if self.stockType == 0:\n", "\n", "            if self.netPosition < 100:\n", "\n", "                return 0\n", "\n", "        else:\n", "\n", "            if self.netPosition < 200:\n", "\n", "                return 0\n", "\n", "        if self.openPrice > self.maxDrawDownRatio * marketData.last_price:\n", "\n", "            return 1"]}, {"cell_type": "markdown", "id": "9dcf48b0", "metadata": {}, "source": ["# from xy_pywrapper import *\n", "## 从模块继承基类StrategyInterface  \n", "### 继承类的方法，类的属性，\n", "### 同时调用xy_pywrapper模块的方法及属性"]}, {"cell_type": "markdown", "id": "ba0b6fc8", "metadata": {}, "source": ["## 收到Tick行情\n", "    \n", "## XyMarketData md 股票快照行情\n", "                string  symbol                   股票代码\n", "                int     status                   状态\n", "                int     pre_close                前收盘价\n", "                int     open_price               开盘价\n", "                int     high_price               最高价\n", "                int     low_price                最低价\n", "                int     last_price               最新价\n", "                int     high_limited             涨停价\n", "                int     low_limited              跌停价\n", "                int     volume                   成交量\n", "                int     turnover                 成交额\n", "                int     weighted_avg_bid_price   加权平均委买价格\n", "                int     total_bid_vol            委托买入总量\n", "                int     weighted_avg_ask_price   加权平均委卖价格\n", "                int     total_ask_vol            委托卖出总量\n", "                int     ask_price[0]             申卖价 0-9\n", "                int     bid_price[0]             申买价 0-9\n", "                int     ask_vol[0]               申卖量 0-9\n", "                int     bid_vol[0]               申买量 0-9\n", "                int     trading_day              交易日\n", "                int     update_time              时间(HHMMSSmmm)\n", "                int     num_trades               成交笔数\n", "                int     iopv                     IOPV净值估值\n", "                int     yield_to_maturity        到期收益率\n", "                int     syl1                     市盈率1\n", "                int     syl2                     市盈率2\n", "                int     sd2                      升跌2（对比上一笔）\n", "        \"\"\""]}, {"attachments": {"%E6%8D%95%E8%8E%B7.PNG": {"image/png": "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"}}, "cell_type": "markdown", "id": "d883143f", "metadata": {}, "source": ["![%E6%8D%95%E8%8E%B7.PNG](attachment:%E6%8D%95%E8%8E%B7.PNG)\n", "\n"]}, {"attachments": {"%E4%B8%BB%E5%8A%A8%E8%B0%83%E7%94%A8%E5%9F%BA%E7%B1%BB%E6%96%B9%E6%B3%95.PNG": {"image/png": "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"}}, "cell_type": "markdown", "id": "6564f928", "metadata": {}, "source": ["![%E4%B8%BB%E5%8A%A8%E8%B0%83%E7%94%A8%E5%9F%BA%E7%B1%BB%E6%96%B9%E6%B3%95.PNG](attachment:%E4%B8%BB%E5%8A%A8%E8%B0%83%E7%94%A8%E5%9F%BA%E7%B1%BB%E6%96%B9%E6%B3%95.PNG)"]}, {"attachments": {"%E8%AE%A2%E5%8D%95%E4%BF%A1%E6%81%AF.PNG": {"image/png": "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"}}, "cell_type": "markdown", "id": "d677457e", "metadata": {}, "source": ["![%E8%AE%A2%E5%8D%95%E4%BF%A1%E6%81%AF.PNG](attachment:%E8%AE%A2%E5%8D%95%E4%BF%A1%E6%81%AF.PNG)"]}, {"cell_type": "code", "execution_count": 26, "id": "585a2381", "metadata": {}, "outputs": [], "source": ["class PyStrategy(StrategyInterface):\n", "    def __init__(self, server):\n", "        # python和C++接口对象初始化，此行不要改动\n", "        StrategyInterface.__init__(self, server)\n", "        self.__count = 0\n", "        self.__account = \"fak001\""]}, {"cell_type": "markdown", "id": "638986c2", "metadata": {}, "source": ["## 订阅股票，初始化传参，实例化生成信号的类，实例化管理报单的类\n", "### 仅确认行情订阅是否成功，未调用类方法"]}, {"cell_type": "code", "execution_count": 27, "id": "c864b1a6", "metadata": {}, "outputs": [], "source": ["    def <PERSON><PERSON><PERSON><PERSON>(self):\n", "        '''订阅股票，初始化传参，实例化生成信号的类，实例化管理报单的类'''\n", "\n", "        #订阅股票\n", "        rtn = self.SubStock(['300750.SZ'],1)\n", "        if rtn:\n", "            self.LogError('行情订阅失败')\n", "            return False\n", "\n", "\n", "        #模型参数(下一版会考虑用json传入)\n", "        minSizeRatio = 2.0\n", "        minMovement = 0.5\n", "        maxDuration = 100.0\n", "        maxSlipLong = 8.0\n", "        maxSizeRatio = 0.5\n", "        maxMovement = -0.5\n", "        maxSlipShort = 5.0\n", "        spacePercent = 0.001\n", "        platePercent = 0.002\n", "        spaceTimeGap = 0.2\n", "        plateTimeGap = 0.2\n", "        maxNetPosition = 300\n", "        maxVolume = 900\n", "        stockType = 0\n", "        warningLimit = 20\n", "        startTime = 94000000\n", "        endTime = 145000000\n", "        quantLimit = 100\n", "        openLimit = 0.05\n", "        stopLimit = 0.9\n", "        limitOrderDuration = 1\n", "        maxDrawDownRatio = 1.05\n", "\n", "        #初始化两个类，一个用于生成信号，一个用于管理报单\n", "        self.signalManagement = marketDataToSignal(minSizeRatio, minMovement, maxDuration, maxSlipLong, maxSizeRatio,\n", "                                                   maxMovement, maxSlipShort,\n", "                                                   spacePercent, spaceTimeGap, platePercent, plateTimeGap)\n", "\n", "        self.orderManagement = signalToOrder(maxNetPosition, maxVolume, stockType, warningLimit, startTime, endTime,\n", "                                             quantLimit, openLimit, stopLimit, limitOrderDuration,maxDrawDownRatio)\n", "        self.LogInfo('init complete')\n", "        return True"]}, {"cell_type": "code", "execution_count": 28, "id": "9c85c97a", "metadata": {}, "outputs": [], "source": ["    def On<PERSON><PERSON>test<PERSON>(self,json):\n", "        # 回测专用初始化接口，实盘交易系统不会调用，策略需要在此接口中订阅股票行情\n", "\n", "        #trading_day = json['trading_day']\n", "        rtn = self.SubStock(['300750.SZ'],1)\n", "        if rtn:\n", "            self.LogError('行情订阅失败')\n", "            return False\n", "        self.LogInfo('init start')\n", "        minSizeRatio =2.0\n", "        minMovement = 2.0\n", "        maxDuration = 9.0\n", "        maxSlipLong = 5.0\n", "\n", "        maxSizeRatio = 0.5\n", "        maxMovement = -1.0\n", "        maxSlipShort = 5.0\n", "\n", "        spacePercent = 0.001\n", "        platePercent = 0.002\n", "        spaceTimeGap = 0.2\n", "        plateTimeGap = 0.2\n", "\n", "        maxNetPosition = 10000\n", "        maxVolume = 90000\n", "        stockType = 0\n", "        warningLimit = 20\n", "        startTime = 94000000\n", "        endTime = 145000000\n", "        quantLimit = 100\n", "        openLimit = 0.05\n", "        stopLimit = 0.9\n", "        limitOrderDuration = 1\n", "        maxDrawDownRatio = 1.05\n", "\n", "        self.signalManagement= marketDataToSignal(minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort,\n", "                 spacePercent,spaceTimeGap,platePercent,plateTimeGap)\n", "        self.orderManagement = signalToOrder(maxNetPosition,maxVolume,stockType,warningLimit,startTime,endTime,quantLimit,openLimit,stopLimit,limitOrderDuration,maxDrawDownRatio)\n", "        self.LogInfo('init complete')\n", "\n", "\n", "        return True"]}, {"cell_type": "markdown", "id": "c9837fea", "metadata": {}, "source": ["### 打印策略退出信息，调用基类中定义的打印日志信息的方法"]}, {"cell_type": "code", "execution_count": 29, "id": "19f41e56", "metadata": {}, "outputs": [], "source": ["    def On<PERSON><PERSON><PERSON>(self):\n", "        # 策略退出接口。交易系统在安全退出时调用。回测系统在当日结束时调用。\n", "        self.LogInfo('StrategyPrint: OnRelease()')"]}, {"attachments": {"%E6%95%B0%E6%8D%AE%E5%AE%9A%E4%B9%89.PNG": {"image/png": "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"}}, "cell_type": "markdown", "id": "14fa9ade", "metadata": {}, "source": ["![%E6%95%B0%E6%8D%AE%E5%AE%9A%E4%B9%89.PNG](attachment:%E6%95%B0%E6%8D%AE%E5%AE%9A%E4%B9%89.PNG)"]}, {"cell_type": "markdown", "id": "b7c945a9", "metadata": {}, "source": ["## 接收到股票tick行情快照时，调用此方法"]}, {"cell_type": "code", "execution_count": 30, "id": "a64eb00c", "metadata": {}, "outputs": [], "source": [" def OnMarketData(self, marketData):\n", "        # 收到Tick行情\n", "        \n", "        \n", "        #记录下这笔行情\n", "        self.orderManagement.lastMarketData = marketData\n", "\n", "\n", "\n", "        #逼近涨停价，我们就不再开仓了，这里是用于初始化这个逼近涨停价的判断阈值的\n", "        #含义是，如果阈值没有被初始化，就初始化这个价格\n", "        if self.orderManagement.openLimitPrice == 0:\n", "\n", "            self.orderManagement.getOpenLimitPrice(marketData)\n", "\n", "            self.LogInfo('开仓上限价格：%d,'%(self.orderManagement.openLimitPrice))\n", "\n", "\n", "        #逼近跌停价，我们就打算清仓了，这里对这个逼近跌停价的阈值进行初始化\n", "        if self.orderManagement.stopLimitPrice == 0:\n", "\n", "            self.orderManagement.getStopLimitPrice(marketData)\n", "            self.LogInfo('清仓上限价格：%d' % (self.orderManagement.stopLimitPrice))\n", "\n", "\n", "\n", "        #发现跌停\n", "        if marketData.bid_price[0] == 0:\n", "\n", "            #进入清仓模式\n", "            self.orderManagement.stopTrading = 1\n", "\n", "            #计算可平仓位\n", "            volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "            #如果有可平仓位\n", "            if volumeShort > 0:\n", "\n", "                #如果当前有空单处于正报状态，警告+1，连续多次警告，直接报警\n", "                if self.orderManagement.shortSendingOrder == 1:\n", "\n", "                    self.orderManagement.warning +=1\n", "\n", "                    if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "\n", "                        self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                #如果当前有挂着的空单\n", "                elif self.orderManagement.shortPendingOrder == 1:\n", "\n", "                    #如果挂单价不是跌停价或挂单量不等于可平量\n", "                    if self.orderManagement.shortLimitPrice != marketData.low_limited or self.orderManagement.shortLimitVolume != volumeShort:\n", "\n", "                        #撤单\n", "                        order = self.GetOrder(self.orderManagement.shortOrderId)#实例化获取股票信息，该方法的返回值有本地ID属性\n", "                        self.CancelOrder(order.local_order_id, self.orderManagement.shortOrderId)#该方法负责撤单\n", "\n", "                #如果没有挂着的空单，直接跌停价打板\n", "                else:\n", "\n", "                    #报单接口，技术部提供，直接依样画葫芦即可\n", "\n", "                    #用于记录本地报单编号\n", "                    self.__count += 1\n", "                    new_order = ReqNewField()\n", "                    new_order.account = self.__account\n", "                    new_order.symbol = marketData.symbol\n", "                    new_order.price = marketData.low_limited\n", "                    new_order.qty = volumeShort\n", "                    # new_order.direction = Direction.kSell\n", "                    # new_order.exchange = Exchange.kSzse\n", "                    # new_order.open_close = OpenClose.kClose\n", "                    self.NewOrder(new_order, self.__count)#返回值是local_order_id\n", "\n", "                    #更新空头报单的状态\n", "                    self.orderManagement.shortOrderId = self.__count\n", "                    self.orderManagement.shortSendingOrder = 1\n", "                    self.orderManagement.shortSendPrice = marketData.low_limited\n", "                    self.orderManagement.shortSendVolume = volumeShort\n", "                    self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "        #发现涨停\n", "        elif marketData.ask_price[0] == 0:\n", "\n", "            #计算可平仓位\n", "            volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "            if volumeShort > 0:\n", "\n", "                #如果有空头正报或者空头正挂状态，warning加1，warning过大，报警\n", "                if self.orderManagement.shortSendingOrder == 1 or self.orderManagement.shortPendingOrder == 1:\n", "\n", "                    self.orderManagement.warning += 1\n", "\n", "                    if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "\n", "                        self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                #正常状态下，涨停价平仓\n", "                else:\n", "\n", "                    self.__count += 1\n", "                    new_order = ReqNewField()\n", "                    new_order.account = self.__account\n", "                    new_order.symbol = marketData.symbol\n", "                    new_order.price = marketData.high_limited\n", "                    new_order.qty = volumeShort\n", "                    # new_order.direction = Direction.kSell\n", "                    # new_order.exchange = Exchange.kSzse\n", "                    # new_order.open_close = OpenClose.kClose\n", "                    self.NewOrder(new_order, self.__count)\n", "\n", "                    self.orderManagement.shortOrderId = self.__count\n", "                    self.orderManagement.shortSendingOrder = 1\n", "                    self.orderManagement.shortSendPrice = marketData.high_limited\n", "                    self.orderManagement.shortSendVolume = volumeShort\n", "                    self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "        #正常行情\n", "        else:\n", "\n", "            #更新交易信号\n", "            self.signalManagement.update(marketData)\n", "            #记录下当下的信号值\n", "            signal = self.signalManagement.signal\n", "\n", "            #如果还没有开始交易\n", "            if self.orderManagement.startTrading == 0:\n", "\n", "                #累积行情数量加1\n", "                self.orderManagement.quantOfMarketData +=1\n", "\n", "                #累积行情数量足够且最新行情的时间戳大于开始交易时间，开始交易\n", "                if self.orderManagement.quantOfMarketData > self.orderManagement.quantLimit and marketData.update_time >self.orderManagement.startTime:\n", "\n", "                    self.orderManagement.startTrading = 1\n", "                    self.LogInfo('startTrading')\n", "\n", "            #如果已经开始交易\n", "            else:\n", "\n", "                #如果还没有进入清仓状态\n", "                if self.orderManagement.stopTrading == 0:\n", "\n", "                    #价格逼近跌停或行情时间逼近收盘或触发止损，进入清仓状态，并退出\n", "                    if marketData.ask_price[0] <= self.orderManagement.stopLimitPrice or marketData.update_time > \\\n", "                        self.orderManagement.endTime or self.orderManagement.getStopFlag(marketData):\n", "\n", "                        self.orderManagement.stopTrading = 1\n", "                        self.LogInfo('stopTrading, time:%d'%(marketData.update_time))\n", "\n", "                        return\n", "\n", "                    #没有交易信号\n", "                    if signal == 0:\n", "\n", "                        #空头单处于正报，警告+1，连续警告过多，报警\n", "                        if self.orderManagement.shortSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #空头单处于正挂\n", "                        elif self.orderManagement.shortPendingOrder == 1:\n", "\n", "                            #判断挂单是否超时，如果超时，撤单\n", "                            if timeGap(self.orderManagement.shortOrderTime,\n", "                                       marketData.update_time) > self.orderManagement.limitOrderDuration - point:\n", "\n", "                                order = self.GetOrder(self.orderManagement.shortOrderId)\n", "                                self.CancelOrder(order.local_order_id, self.orderManagement.shortOrderId)\n", "\n", "                        #多头单正报，警告+1，连续警告过多，报警\n", "                        if self.orderManagement.longSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #多头单正挂，看是否超时，超时撤单\n", "                        elif self.orderManagement.longPendingOrder == 1:\n", "\n", "                            if timeGap(self.orderManagement.longOrderTime,\n", "                                       marketData.update_time) > self.orderManagement.limitOrderDuration - point:\n", "                                order = self.GetOrder(self.orderManagement.longOrderId)\n", "                                self.CancelOrder(order.local_order_id, self.orderManagement.longOrderId)\n", "\n", "                    #空头信号\n", "                    elif signal == -1:\n", "\n", "                        #多头单正报状态，警告+1，警告过多，直接报警\n", "                        if self.orderManagement.longSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #多头单正挂，赶紧撤\n", "                        elif self.orderManagement.longPendingOrder == 1:\n", "\n", "                            order = self.GetOrder(self.orderManagement.longOrderId)\n", "                            self.CancelOrder(order.local_order_id, self.orderManagement.longOrderId)\n", "\n", "                        #空头单正报，警告+1，警告过多，报警\n", "                        if self.orderManagement.shortSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #空头单正挂，直接撤，准备追单\n", "                        elif self.orderManagement.shortPendingOrder == 1:\n", "\n", "                            order = self.GetOrder(self.orderManagement.shortOrderId)\n", "                            self.CancelOrder(order.local_order_id, self.orderManagement.shortOrderId)\n", "\n", "                        #没有空头单\n", "                        else:\n", "\n", "                            #计算可平量\n", "                            volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "                            #可平量大于0\n", "                            if volumeShort > 0:\n", "\n", "                                #没有多头单\n", "                                if self.orderManagement.longSendingOrder == 0 and self.orderManagement.longPendingOrder == 0:\n", "\n", "                                    #报空单，打对价\n", "                                    self.__count += 1\n", "                                    new_order = ReqNewField()\n", "                                    new_order.account = self.__account\n", "                                    new_order.symbol = marketData.symbol\n", "                                    new_order.price = marketData.bid_price[0]\n", "                                    new_order.qty = volumeShort\n", "                                    # new_order.direction = Direction.kSell\n", "                                    # new_order.exchange = Exchange.kSzse\n", "                                    # new_order.open_close = OpenClose.kClose\n", "                                    self.NewOrder(new_order, self.__count)\n", "\n", "                                    self.orderManagement.shortOrderId = self.__count\n", "                                    self.orderManagement.shortSendingOrder = 1\n", "                                    self.orderManagement.shortSendPrice = marketData.bid_price[0]\n", "                                    self.orderManagement.shortSendVolume = volumeShort\n", "                                    self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "                    #多头信号\n", "                    else:\n", "\n", "                        #空头单正报，警告+1，连续警告过多，报警\n", "                        if self.orderManagement.shortSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #空头单正挂，赶紧撤\n", "                        elif self.orderManagement.shortPendingOrder == 1:\n", "\n", "                            order = self.GetOrder(self.orderManagement.shortOrderId)\n", "                            self.CancelOrder(order.local_order_id, self.orderManagement.shortOrderId)\n", "\n", "                        #多头单正报，警告+1，连续警告过多，报警\n", "                        if self.orderManagement.longSendingOrder == 1:\n", "\n", "                            self.orderManagement.warning += 1\n", "\n", "                            if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                        #多头单正挂，撤掉，准备追单\n", "                        elif self.orderManagement.longPendingOrder == 1:\n", "\n", "                            order = self.GetOrder(self.orderManagement.longOrderId)\n", "                            self.CancelOrder(order.local_order_id, self.orderManagement.longOrderId)\n", "\n", "                        #没有多头单\n", "                        else:\n", "\n", "                            #计算可开仓位\n", "                            volumeLong = self.orderManagement.getVolumeLong()\n", "\n", "                            #如果可开仓位大于0\n", "                            if volumeLong > 0:\n", "\n", "                                #没有空头单\n", "                                if self.orderManagement.shortSendingOrder == 0 and self.orderManagement.shortPendingOrder == 0:\n", "\n", "                                    #价格没有逼近涨停\n", "                                    if self.orderManagement.openLimitPrice > marketData.ask_price[0]:\n", "\n", "                                        #打对价开仓\n", "                                        self.LogInfo(\"准备报单，时间：%d\" % (marketData.update_time))\n", "                                        self.__count += 1\n", "                                        new_order = ReqNewField()\n", "                                        new_order.account = self.__account\n", "                                        new_order.symbol = marketData.symbol\n", "                                        new_order.price = marketData.ask_price[0]\n", "                                        new_order.qty = volumeLong\n", "                                        # new_order.direction = Direction.kBuy\n", "                                        # new_order.exchange = Exchange.kSzse\n", "                                        # new_order.open_close = OpenClose.kOpen\n", "                                        self.NewOrder(new_order, self.__count)\n", "\n", "                                        self.orderManagement.longOrderId = self.__count\n", "                                        self.orderManagement.longSendingOrder = 1\n", "                                        self.orderManagement.longSendPrice = marketData.bid_price[0]\n", "                                        self.orderManagement.longSendVolume = volumeLong\n", "                                        self.orderManagement.longOrderTime = marketData.update_time\n", "                #进入清仓阶段\n", "                else:\n", "\n", "                    #处理多单正报\n", "                    if self.orderManagement.longSendingOrder == 1:\n", "\n", "                        self.orderManagement.warning += 1\n", "\n", "                        if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                            self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                    #多单正挂，撤单\n", "                    elif self.orderManagement.longPendingOrder == 1:\n", "\n", "                        order = self.GetOrder(self.orderManagement.longOrderId)\n", "                        self.CancelOrder(order.local_order_id, self.orderManagement.longOrderId)\n", "\n", "                    #没有多单\n", "                    else:\n", "\n", "                        #计算可平空单\n", "                        volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "                        if volumeShort > 0:\n", "\n", "                            #处理空单正报\n", "                            if self.orderManagement.shortSendingOrder == 1:\n", "\n", "                                self.orderManagement.warning += 1\n", "\n", "                                if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "                                    self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "                            #空单正挂，如果超时了，撤单\n", "                            elif self.orderManagement.shortPendingOrder == 1:\n", "\n", "                                if timeGap(self.orderManagement.shortOrderTime,marketData.update_time) >self.orderManagement.limitOrderDuration - point:\n", "                                    order = self.GetOrder(self.orderManagement.shortOrderId)\n", "                                    self.CancelOrder(order.local_order_id, self.orderManagement.shortOrderId)\n", "\n", "                            #没有空头，打对价清仓\n", "                            else:\n", "\n", "                                self.__count += 1\n", "                                new_order = ReqNewField()\n", "                                new_order.account = self.__account\n", "                                new_order.symbol = marketData.symbol\n", "                                new_order.price = marketData.bid_price[0]\n", "                                new_order.qty = volumeShort\n", "                                # new_order.direction = Direction.kSell\n", "                                # new_order.exchange = Exchange.kSzse\n", "                                # new_order.open_close = OpenClose.kClose\n", "                                self.NewOrder(new_order, self.__count)\n", "\n", "                                self.orderManagement.shortOrderId = self.__count\n", "                                self.orderManagement.shortSendingOrder = 1\n", "                                self.orderManagement.shortSendPrice = marketData.bid_price[0]\n", "                                self.orderManagement.shortSendVolume = volumeShort\n", "                                self.orderManagement.shortOrderTime = marketData.update_time"]}, {"cell_type": "markdown", "id": "3eba91cb", "metadata": {}, "source": ["## 收到逐笔成交，逐笔委托，指数行情\n", "## 如不用这些数据，可以直接pass,什么也不做。但必须要有对应的方法，行情系统需要调用这些方法，或者说接口。"]}, {"cell_type": "code", "execution_count": 31, "id": "ab36b18f", "metadata": {}, "outputs": [], "source": ["    def OnTrans(self, trans_md):\n", "\n", "        return\n", "\n", "    def OnOrder(self, order_md):\n", "\n", "        return\n", "\n", "    def OnIndex(self, index_md):\n", "\n", "        return"]}, {"cell_type": "markdown", "id": "079b0f8f", "metadata": {}, "source": ["# 收到报单接受回报\n", "\n", "\n", "        RspAcceptedField accept 报单接受响应\n", "            string    account          资金账号\n", "            string    symbol           股票代码\n", "            string    entrust_no       系统报单编号\n", "            int       local_order_id   本地报单编号\n", "            int       price            委托价格\n", "            int       qty              委托数量\n", "            int       accepted_qty     报单接受量\n", "            int       remote_order_id  易迅柜台报单编号\n", "            int       accept_time      系统订单时间\n", "            Direction direction        买卖方向\n", "            OpenClose open_close       开平方向\n"]}, {"cell_type": "code", "execution_count": 32, "id": "59e44124", "metadata": {}, "outputs": [], "source": ["  def OnOrderAccept(self, accept, req_id):\n", "\n", "        self.LogInfo('OnOrderAccept, order_id: %d, req_id: %d' %\n", "                     (accept.local_order_id, req_id))\n", "\n", "        #更新挂单状态，清零警告\n", "        if self.orderManagement.longOrderId == req_id:\n", "            self.orderManagement.longSendingOrder = 0\n", "            self.orderManagement.warning = 0\n", "            self.orderManagement.longPendingOrder = 1\n", "            self.orderManagement.longLimitPrice = accept.price\n", "            self.orderManagement.longLimitVolume = accept.qty\n", "\n", "        if self.orderManagement.shortOrderId == req_id:\n", "            self.orderManagement.shortSendingOrder = 0\n", "            self.orderManagement.warning = 0\n", "            self.orderManagement.shortPendingOrder = 1\n", "            self.orderManagement.shortLimitPrice = accept.price\n", "            self.orderManagement.shortLimitVolume = accept.qty\n", "\n", "        return"]}, {"cell_type": "markdown", "id": "807d75f7", "metadata": {}, "source": ["# 收到报单拒绝回报\n", "\n", "        RspRejectedField reject 报单拒绝响应\n", "            string  account         资金账号\n", "            string  symbol          股票代码\n", "            string  error_msg       错误消息\n", "            int     local_order_id  本地报单编号\n", "            int     price           委托价格\n", "            int     qty             委托数量\n", "            int     rejected_qty    报单拒绝量\n", "            int     error_code      错误码\n", "            Direction direction     买卖方向\n", "            OpenClose open_close    开平方向\n"]}, {"cell_type": "code", "execution_count": 33, "id": "6e5a75d4", "metadata": {}, "outputs": [], "source": ["    def OnOrderReject(self, reject, req_id):\n", "\n", "\n", "        self.LogInfo('OnOrderReject, order_id: %d, req_id: %d' %\n", "                     (reject.local_order_id, req_id))\n", "\n", "        #更新正报状态的订单状态\n", "        if self.orderManagement.longOrderId == req_id:\n", "            self.orderManagement.longSendingOrder = 0\n", "\n", "        if self.orderManagement.shortOrderId == req_id:\n", "            self.orderManagement.shortSendingOrder = 0\n", "\n", "        #报警次数加1\n", "        self.orderManagement.warning += 1\n", "\n", "        #连续报警次数过大，则，报警\n", "        if self.orderManagement.warning > self.orderManagement.warningLimit:\n", "            self.Log<PERSON><PERSON>r('无法报单')\n", "\n", "        return"]}, {"cell_type": "markdown", "id": "db5a2264", "metadata": {}, "source": ["# 收到订单成交回报\n", "\n", "        RspExecutionField trade 成交响应\n", "            string  account              资金账号\n", "            string  symbol               股票代码\n", "            string  entrust_no           系统报单编号\n", "            string  trade_id             成交编号\n", "            int     local_order_id       本地报单编号\n", "            int     price                委托价格\n", "            int     trade_price          成交价格\n", "            int     qty                  委托数量\n", "            int     trade_qty            成交数量\n", "            int     remote_order_id      易迅柜台报单编号\n", "            int     time                 成交时间\n", "            Direction  direction         买卖方向\n", "            OpenClose  open_close        开平方向\n", "            MatchingType  matching_type  撮合类型\n"]}, {"cell_type": "code", "execution_count": 34, "id": "69433b99", "metadata": {}, "outputs": [], "source": [" def OnTrade(self, trade, req_id):\n", "\n", "        self.LogInfo('OnTrade, order_id: %d, req_id: %d，trade_qty：%d,update_time:%d' %\n", "                     (trade.local_order_id, req_id,trade.trade_qty,self.orderManagement.lastMarketData.update_time))\n", "\n", "        #多头单成交\n", "        if self.orderManagement.longOrderId == req_id:\n", "            #多头单已成交量加上本次成交量\n", "            self.orderManagement.totalTradeQuantLong +=trade.trade_qty\n", "\n", "            #如果委托量等于已成交量，即，全部成交\n", "            if trade.qty == self.orderManagement.totalTradeQuantLong:\n", "\n", "                #清除状态\n", "                self.orderManagement.totalTradeQuantLong = 0\n", "                self.orderManagement.longPendingOrder = 0\n", "                self.orderManagement.longSendingOrder = 0\n", "                self.orderManagement.warning = 0\n", "\n", "                #更新净持仓和总成交量\n", "                tradeVolume = trade.qty\n", "                self.orderManagement.netPosition += tradeVolume\n", "                self.orderManagement.totalVolume += tradeVolume\n", "                #记录开仓价格\n", "                self.orderManagement.openPrice = trade.price\n", "\n", "        #空头单成交\n", "        if self.orderManagement.shortOrderId == req_id:\n", "            #空头单已成交量加上本次成交量\n", "            self.orderManagement.totalTradeQuantShort +=trade.trade_qty\n", "            #委托量等于已成交量，即，全部成交\n", "            if trade.qty == self.orderManagement.totalTradeQuantShort:\n", "                #清空状态\n", "                self.orderManagement.totalTradeQuantShort = 0\n", "                self.orderManagement.shortPendingOrder = 0\n", "                self.orderManagement.shortSendingOrder = 0\n", "                self.orderManagement.warning = 0\n", "                #更新净持仓\n", "                tradeVolume = trade.qty\n", "                self.orderManagement.netPosition -= tradeVolume\n", "        return\n"]}, {"cell_type": "markdown", "id": "b06de1e2", "metadata": {}, "source": ["# 收到撤单接受回报\n", "\n", "            RspCancelAcceptedField cxl_accept 撤单接受响应\n", "                string  account          资金账号\n", "                string  symbol           股票代码\n", "                string  entrust_no       系统报单编号\n", "                int     local_order_id   本地报单编号\n", "                int     price            委托价格\n", "                int     qty              委托数量\n", "                int     cancel_qty       撤单数量\n", "                Direction  direction     买卖方向\n", "                OpenClose  open_close    开平方向\n"]}, {"cell_type": "code", "execution_count": null, "id": "66c13293", "metadata": {}, "outputs": [], "source": ["  def OnCancelAccept(self, cxl_accept, req_id):\n", "\n", "        self.LogInfo('OnCancelAccept, order_id: %d, req_id: %d' %\n", "                     (cxl_accept.local_order_id, req_id))\n", "\n", "        #多头撤单接受\n", "        if self.orderManagement.longOrderId == req_id:\n", "            #清空了状态\n", "            self.orderManagement.totalTradeQuantLong = 0\n", "            self.orderManagement.longPendingOrder = 0\n", "            self.orderManagement.longSendingOrder = 0\n", "            self.orderManagement.warning = 0\n", "            #计算成交量\n", "            tradeVolume = cxl_accept.qty - cxl_accept.cancel_qty\n", "            #更新持仓信息\n", "            self.orderManagement.netPosition += tradeVolume\n", "            self.orderManagement.totalVolume += tradeVolume\n", "            #如果有成交，更新开仓价格\n", "            if tradeVolume >0:\n", "                self.orderManagement.openPrice = cxl_accept.price\n", "\n", "        #空头撤单接受\n", "        if self.orderManagement.shortOrderId == req_id:\n", "            self.orderManagement.totalTradeQuantShort = 0\n", "            self.orderManagement.shortPendingOrder = 0\n", "            self.orderManagement.shortSendingOrder = 0\n", "            self.orderManagement.warning = 0\n", "            #计算成交量\n", "            tradeVolume = cxl_accept.qty - cxl_accept.cancel_qty\n", "            #更新持仓信息\n", "            self.orderManagement.netPosition -= tradeVolume\n", "\n", "        #读取最新行情\n", "        marketData = self.orderManagement.lastMarketData\n", "\n", "        #跌停情况\n", "        if marketData.bid_price[0] == 0:\n", "\n", "\n", "            volumeShort = self.orderManagement.getVolumeShort()\n", "            #有空头可平量，打板\n", "            if volumeShort > 0:\n", "\n", "                self.__count += 1\n", "                new_order = ReqNewField()\n", "                new_order.account = self.__account\n", "                new_order.symbol = marketData.symbol\n", "                new_order.price = marketData.low_limited\n", "                new_order.qty = volumeShort\n", "                # new_order.direction = Direction.kSell\n", "                # new_order.exchange = Exchange.kSzse\n", "                # new_order.open_close = OpenClose.kClose\n", "                self.NewOrder(new_order, self.__count)\n", "\n", "                self.orderManagement.shortOrderId = self.__count\n", "                self.orderManagement.shortSendingOrder = 1\n", "                self.orderManagement.shortSendPrice = marketData.low_limited\n", "                self.orderManagement.shortSendVolume = volumeShort\n", "                self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "        #涨停\n", "        elif marketData.ask_price[0] == 0:\n", "\n", "            volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "            if volumeShort > 0:\n", "\n", "                self.__count += 1\n", "                new_order = ReqNewField()\n", "                new_order.account = self.__account\n", "                new_order.symbol = marketData.symbol\n", "                new_order.price = marketData.high_limited\n", "                new_order.qty = volumeShort\n", "                # new_order.direction = Direction.kSell\n", "                # new_order.exchange = Exchange.kSzse\n", "                # new_order.open_close = OpenClose.kClose\n", "                self.NewOrder(new_order, self.__count)\n", "\n", "                self.orderManagement.shortOrderId = self.__count\n", "                self.orderManagement.shortSendingOrder = 1\n", "                self.orderManagement.shortSendPrice = marketData.high_limited\n", "                self.orderManagement.shortSendVolume = volumeShort\n", "                self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "        #正常情况\n", "        else:\n", "            #把信号读过来\n", "            signal = self.signalManagement.signal\n", "\n", "            #正常状态\n", "            if self.orderManagement.stopTrading == 0:\n", "\n", "                #空头信号处理\n", "                if signal == -1:\n", "\n", "                    volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "                    if volumeShort > 0:\n", "\n", "                        if self.orderManagement.longSendingOrder == 0 and self.orderManagement.longPendingOrder == 0:\n", "\n", "                            self.__count += 1\n", "                            new_order = ReqNewField()\n", "                            new_order.account = self.__account\n", "                            new_order.symbol = marketData.symbol\n", "                            new_order.price = marketData.bid_price[0]\n", "                            new_order.qty = volumeShort\n", "                            # new_order.direction = Direction.kSell\n", "                            # new_order.exchange = Exchange.kSzse\n", "                            # new_order.open_close = OpenClose.kClose\n", "                            self.NewOrder(new_order, self.__count)\n", "\n", "                            self.orderManagement.shortOrderId = self.__count\n", "                            self.orderManagement.shortSendingOrder = 1\n", "                            self.orderManagement.shortSendPrice = marketData.bid_price[0]\n", "                            self.orderManagement.shortSendVolume = volumeShort\n", "                            self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "                #多头信号处理\n", "                elif signal == 1:\n", "\n", "                    volumeLong = self.orderManagement.getVolumeLong()\n", "\n", "                    if volumeLong > 0:\n", "\n", "                        if self.orderManagement.shortSendingOrder == 0 and self.orderManagement.shortPendingOrder == 0:\n", "\n", "                            if self.orderManagement.openLimitPrice > marketData.ask_price[0]:\n", "\n", "                                self.__count += 1\n", "                                new_order = ReqNewField()\n", "                                new_order.account = self.__account\n", "                                new_order.symbol = marketData.symbol\n", "                                new_order.price = marketData.ask_price[0]\n", "                                new_order.qty = volumeLong\n", "                                # new_order.direction = Direction.kBuy\n", "                                # new_order.exchange = Exchange.kSzse\n", "                                # new_order.open_close = OpenClose.kOpen\n", "                                self.NewOrder(new_order, self.__count)\n", "\n", "                                self.orderManagement.longOrderId = self.__count\n", "                                self.orderManagement.longSendingOrder = 1\n", "                                self.orderManagement.longSendPrice = marketData.bid_price[0]\n", "                                self.orderManagement.longSendVolume = volumeLong\n", "                                self.orderManagement.longOrderTime = marketData.update_time\n", "            #清仓状态处理\n", "            else:\n", "                volumeShort = self.orderManagement.getVolumeShort()\n", "\n", "                if volumeShort > 0:\n", "                    self.__count += 1\n", "                    new_order = ReqNewField()\n", "                    new_order.account = self.__account\n", "                    new_order.symbol = marketData.symbol\n", "                    new_order.price = marketData.bid_price[0]\n", "                    new_order.qty = volumeShort\n", "                    # new_order.direction = Direction.kSell\n", "                    # new_order.exchange = Exchange.kSzse\n", "                    # new_order.open_close = OpenClose.kClose\n", "                    self.NewOrder(new_order, self.__count)\n", "\n", "                    self.orderManagement.shortOrderId = self.__count\n", "                    self.orderManagement.shortSendingOrder = 1\n", "                    self.orderManagement.shortSendPrice = marketData.bid_price[0]\n", "                    self.orderManagement.shortSendVolume = volumeShort\n", "                    self.orderManagement.shortOrderTime = marketData.update_time\n", "\n", "        return\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 5}