import os
path='C:\\Users\\<USER>\\Desktop\\数据示例\\'


def path_name(startDate, endDate, stockCode):
    list_data = []
    for root,dirs,files in os.walk(path):
        for file in files:
            if stockCode in file:
                if startDate<=int(file.split('_')[0])<=endDate:
                    list_data.append(os.path.join(root,file))
    return list_data

# print(path_name(20190606,20190718,'000001.SZ'))
# list_data=['C:\\Users\\<USER>\\Desktop\\数据示例\\20190604_000001.SZ.csv', 'C:\\Users\\<USER>\\Desktop\\数据示例\\20190604_300274.SZ.csv']


