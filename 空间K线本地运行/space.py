# -*- coding: utf-8 -*-
point = 0.000001

def getSpaceList(askPriceList,bidPriceList,width):

    #记录下每个tick的空间因子值
    spaceList=[]

    #初始价格
    price = 0.5*(askPriceList[0][0] + bidPriceList[0][0])
    space = 0

    for i in range(0,len(askPriceList)):

        #买一超过了原空间K线价格width
        if bidPriceList[i][0] - price > width - point:

            #如果之前是空头趋势
            if space < point:
                space = 1
            else:
                space += 1
            #新的空间K线诞生，价格为买一
            price = bidPriceList[i][0]

        #卖一低于原空间K线价格超过width
        if price -askPriceList[i][0] > width - point:

            #如果之前是多头趋势
            if space > -point:
                space = -1
            else:
                space -= 1
            #新的空间K线诞生，价格为卖一
            price = askPriceList[i][0]

        #记录下空间因子的值
        spaceList.append(int(space))

    return spaceList

def MovementOrRevert(spaceList,spaceLow,spaceHigh):

    longMovement = 0
    longRevert = 0
    shortMovement = 0
    shortRevert = 0

    for i in range(0,len(spaceList)-1):

        #空间因子值符合范围，且方向向上
        if spaceList[i] >= spaceLow and spaceList[i] <= spaceHigh and spaceList[i]>=1:

            #出现新K线并且同方向
            if spaceList[i+1] - spaceList[i] ==1:
                longMovement +=1
            #出现新K线且反方向
            if spaceList[i+1] == -1:
                longRevert += 1

        #空间因子为负，空头方向，类似处理
        if -spaceList[i] >= spaceLow and -spaceList[i] <= spaceHigh and spaceList[i]<=-1:

            if spaceList[i + 1] - spaceList[i] == -1:
                shortMovement += 1

            if spaceList[i + 1] == 1:
                shortRevert += 1

    return longMovement,longRevert,shortMovement,shortRevert