import datetime
'''
本模块内容：
输入起始日期的两个字符串格式日期，获取起始日期区间内的字符串格式的日期列表
时间的格式转换，获取时间段内的不同间隔的时间和日期，有各种各样的实现，也可以通过获取交易日历（有很多第三方数据库都提供有类似服务）等方式直接获取
'''

def dateToDateTime(date):
    '''
    :param date: str->'20200815'
    :return: datetime.date->2020-08-15
    '''
    strDate = str(date)
    year = int(strDate[0:4])
    month = int(strDate[4:6])
    day = int(strDate[6:])
    return datetime.date(year,month,day)


def dateTimeToDate(datetime):
    '''
    :param datetime: ->datetime.date 2022-08-15
    :return: str->20220815
    '''

    year = datetime.year
    month = datetime.month
    day = datetime.day

    if len(str(day)) ==2:
        strDay = str(day)
    if len(str(day)) ==1:
        strDay = "0"+str(day)

    if len(str(month)) ==2:
        strMonth = str(month)
    if len(str(month)) ==1:
        strMonth = "0"+str(month)

    strDate = str(year) + strMonth+ strDay
    return strDate




def getDateList(startDate,endDate):
    '''
    :param startDate: '20200815'起始日期的字符串格式
    :param endDate:  '20200819'结束日期的字符串格式
    :return: 返回列表['20200815', '20200816', '20200817', '20200818', '20200819']

    '''

    dateList = []
    datetimeStart = dateToDateTime(startDate)#起始时间从字符串转换成datetime格式
    datetimeEnd = dateToDateTime(endDate)#结束时间从字符串转换成datetime格式

    '''
    下面内容就是这种用法，计算时间的用法
    import datetime
    today = datetime.date.today()
    today = today - datetime.timedelta(days=0) 
    符号可以加减，days参数可以为正负
    '''
    date = datetimeStart
    oneDay = datetime.timedelta(days=1)

    while date <= datetimeEnd:
        dateList.append(dateTimeToDate(date))
        date += oneDay
    return dateList

if __name__ == '__main__':

    startDate ="20200815"
    endDate = "20200819"
    print(getDateList(startDate,endDate))