import numpy as np
point = 0.000001

def longResist(askPrice,askVolume,width):

    resist = 0

    for i in range(0,10):

        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:

            resist += askVolume[i]

    return resist


 def shortResist(bidPrice,bidVolume,width):
     
     resist = 0 
     
     for i in range (0,10):
         
         if bidPrice[i] > bidPrice[0] - width - point:
             
             resist += bidVolume[i]
             
     return resist


class plateSizeResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.longPlateSizeSignalList = []
        self.shortPlateSizeSignalList = []
     
   def getRationalWidth(self):
       
       Price = (self.askPrice[0][0] + bidPrice[0][0])/2
       
       self.width = np.ceil(Price*self.platePercent*100)/100
       
       
   def getResist(self):
       
       for i in range (0,len(self.askPriceList)):
           
           self.longResistList.append(longResist(self.askPriceList[i],self.askVolume[i],self.width))
           self.shortResistList.append(shortResist(self.bidPriceList[i],self.bidVolumeList[i],self.width))


    def getPlateSizeSignal(self,ratioLow,ratioHigh):

        for i in range(0, len(self.shortResistList)):

            if self.shortResistList[i] >= self.longResistList[i] * ratioLow and self.shortResistList[i] <= self.longResistList[i] * ratioHigh:

                self.longPlateSizeSignalList.append(1)

            else:
                self.longPlateSizeSignalList.append(0)

            if self.longResistList[i] >= self.shortResistList[i] * ratioLow and self.longResistList[i] <= self.shortResistList[i] * ratioHigh:

                self.shortPlateSizeSignalList.append(1)

            else:
                self.shortPlateSizeSignalList.append(0)