# -*- coding: utf-8 -*-
import processData as pd
import plate
import factorScoring as fs
import signalEstimateTwap as set
import getDateList as dl
import numpy as np
from sklearn import linear_model
import matplotlib.pyplot as plt

def getDailyResult(date, stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration):

    stockData = pd.getData(date, stockCode)
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pd.processStockData(
        stockData, startTime, endTime)


    plateResearch = plate.plateResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercent)

    plateResearch.getRationalWidth()
    plateResearch.getResist()
    plateResearch.getPlateSize()

    factorList = plateResearch.sizeRatioList
    scoreList = fs.getScoreList(factorList,factorBoundaryList)
    signalList = fs.getSingleFactorSignalList(scoreList,targetScore)

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    profitListLong = signalEstimate.longSignalEstimateTwap(signalDuration, signalList)

    return profitListLong

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration):

    dateList = dl.getDateList(startDate,endDate)
    totalProfitListLong = []

    for date in dateList:
        try:
            profitListLong = getDailyResult(date, stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration)
        except:
            continue
        else:
            for n in profitListLong:
                totalProfitListLong.append(n)

    if len(totalProfitListLong) ==0:
        meanLong = 0
    else:
        meanLong = np.mean(totalProfitListLong)

    return meanLong,len(totalProfitListLong)

def getRegressionResult(startDate,endDate,stockCode,startTime, endTime,platePercent,tWapDuration,signalDuration,factorBoundaryList):

    alpha = []
    score = []
    for targetScore in range(0,len(factorBoundaryList)+1):

        profitLong,lengthLong = getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration)
        print(targetScore,profitLong,lengthLong)
        if lengthLong >= 50:
            alpha.append(profitLong)
            score.append([targetScore])

    #线性回归模型
    model = linear_model.LinearRegression()
    model.fit(score, alpha)
    print(model.coef_)
    print(model.intercept_)
    print(model.score(score, alpha))

    #线性模型的预测值
    expectAlpha = []
    for i in range(0,len(score)):
        expectAlpha.append([score[i][0]*model.coef_[0]+model.intercept_])

    #画图
    plt.plot(score,alpha,'ro')
    plt.plot(score,expectAlpha)
    plt.show()

if __name__ == '__main__':

    stockCode = "603396.SH"
    startTime = 93100
    endTime = 145000
    platePercent = 0.002

    startDate = 20220101
    endDate = 20220930

    factorBoundaryList =[0.011,0.018,0.031,0.053,0.083,0.14,0.22,0.4,0.7,1.4,2.5,4.5,7.2,12.0,19.0,32.0,55.0,90.0]
    #factorBoundaryList = [ 0.018,  0.053,  0.14, 0.4, 2.5,  7.2,  19.0,  55.0,]
    tWapDuration = 60
    signalDuration=60

    getRegressionResult(startDate, endDate, stockCode, startTime, endTime, platePercent, tWapDuration, signalDuration,
                        factorBoundaryList)





