# -*- coding: utf-8 -*-
point = 0.000001

#输入因子值和区间列，输出打分
def getFactorScore(factor,factorBoundaryList):

    score = 0

    while score < len(factorBoundaryList) - point:

        if factor < factorBoundaryList[int(score)] + point:

            return int(score)

        score += 1

    return int(score)

#输入因子列，输出打分序列
def getScoreList(factorList,factorBoundaryList):

    scoreList = []

    for i in range(0,len(factorList)):

        scoreList.append(getFactorScore(factorList[i],factorBoundaryList))

    return scoreList

#用目标打分筛选出打分等于目标打分的那些点
def getSingleFactorSignalList(scoreList,targetScore):

    signalList = []

    for i in range(0,len(scoreList)):

        if scoreList[i] == targetScore:
            signalList.append(1)
        else:
            signalList.append(0)

    return signalList

#两个信号混合
def combineSignal(signalList1,signalList2):

    signalList = []
    for i in range(0,len(signalList1)):

        if signalList1[i]==1 and signalList2[i]==1:
            signalList.append(1)
        else:
            signalList.append(0)

    return signalList
