"""
Copyright (c) 2016-2020, XYAsset
Author: <PERSON><PERSON><PERSON>
Create Date: 2020-10-15 08:00:00
"""
import codecs
import json
import math
import time

import pandas
from xy_pywrapper import *

point = 0.000001

def getEffectivePrice(priceList):
    # 有效盘口范围，去除涨跌停
    i = 1
    while i <= len(priceList):
        if priceList[-i] > point:
            return priceList[-i]
        i += 1
    return priceList[0]

def getAskPriceLimit(ask_price, lastAskPrice, width):
    # 卖盘统计范围上限
    maxAsk = getEffectivePrice(ask_price)
    maxLastAsk = getEffectivePrice(lastAskPrice)
    return min(maxAsk, maxLastAsk, max(ask_price[0], lastAskPrice[0]) + width)

def getBidPriceLimit(bid_price, lastBidPrice, width):
    # 买盘统计范围下限
    minBid = getEffectivePrice(bid_price)
    minLastBid = getEffectivePrice(lastBidPrice)
    return max(minBid, minLastBid, min(bid_price[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price, ask_price, ask_vol):
    # 卖盘总盘口量
    volumeBelow = 0
    for i in range(0, len(ask_price)):
        if price + point > ask_price[i] > point:
            volumeBelow += ask_vol[i]
    return volumeBelow

def volumeUpPrice(price, bid_price, bid_vol):
    # 买盘总盘口量
    volumeUp = 0
    for i in range(0, len(bid_price)):
        if bid_price[i] > price - point and bid_price[i] > point:
            volumeUp += bid_vol[i]
    return volumeUp

def getTickMovement(ask_price, bid_price, ask_vol, bid_vol, lastAskPrice, lastBidPrice, lastAskVolume, lastBidVolume,
                    width):
    # 获取盘口动量
    # 定义动量的统计范围
    askPriceLimit = getAskPriceLimit(ask_price, lastAskPrice, width)
    bidPriceLimit = getBidPriceLimit(bid_price, lastBidPrice, width)
    # 卖盘动量
    lastVolumeBelow = volumeBelowPrice(askPriceLimit, lastAskPrice, lastAskVolume)
    volumeBelow = volumeBelowPrice(askPriceLimit, ask_price, ask_vol)
    deltaAsk = lastVolumeBelow - volumeBelow
    # 买盘动量
    lastVolumeUp = volumeUpPrice(bidPriceLimit, lastBidPrice, lastBidVolume)
    volumeUp = volumeUpPrice(bidPriceLimit, bid_price, bid_vol)
    deltaBid = volumeUp - lastVolumeUp

    return deltaAsk + deltaBid

def getLongResist(ask_price, ask_vol, width):
    # 卖盘阻力
    resist = 0
    for i in range(0, 10):
        if ask_price[0] + width + point > ask_price[i] > point:
            resist += ask_vol[i]
    return resist

def getShortResist(bid_price, bid_vol, width):
    # 买盘阻力
    resist = 0
    for i in range(0, 10):
        if bid_price[i] > bid_price[0] - width - point:
            resist += bid_vol[i]
    return resist


def getMillisecondTime(time):
    # 处理时间数据
    time = str(int(time))
    millisecond = int(time[-3:])
    second = int(time[-5:-3])
    minute = int(time[-7:-5])
    hour = int(time[:-7])
    return millisecond, second, minute, hour


def millisecondTimeGap(startTime, endTime):
    # 返回时间差，单位为毫秒
    millisecondStart, secondStart, minuteStart, hourStart = getMillisecondTime(startTime)
    millisecondEnd, secondEnd, minuteEnd, hourEnd = getMillisecondTime(endTime)
    secondBreak = 10
    minuteBreak = 30
    hourBreak = 11
    secondRestart = 50
    minuteRestart = 59
    hourRestart = 12
    gap1 = (hourBreak - hourStart) * 3600000 + (minuteBreak - minuteStart) * 60000 + \
           (secondBreak - secondStart) * 1000 - millisecondStart
    gap2 = (hourEnd - hourRestart) * 3600000 + (minuteEnd - minuteRestart) * 60000 + \
           (secondEnd - secondRestart) * 1000 + millisecondEnd
    if gap1 < 0 or gap2 < 0:
        gap = (hourEnd - hourStart) * 3600000 + (minuteEnd - minuteStart) * 60000 + (secondEnd - secondStart) * 1000 \
              + millisecondEnd - millisecondStart
        return gap
    else:
        return gap1 + gap2 - 19750


class SignalManager:
    # 信号模型, 负责产出交易信号
    def __init__(self, stockCode, symbol, priceTick, config):
        # 初始化
        self.spaceFactor = None
        self.plateFactor = None
        self.stockCode = stockCode
        self.stockCode = symbol
        self.priceTick = priceTick
        # 时间单位调整至毫秒
        self.startTime = config["startTime"] * 1000
        self.endTime = config["endTime"] * 1000
        self.platePercentPlate = config["platePercentPlate"]
        self.platePercentSpace = config["platePercentSpace"]
        self.minSizeRatioOpen = config["minSizeRatioOpen"]
        self.minMovementOpen = config["minMovementOpen"]
        self.maxDurationOpen = config["maxDurationOpen"] * 1000
        self.maxSlipOpen = config["maxSlipOpen"]
        self.maxSizeRatioClose = config["maxSizeRatioClose"]
        self.maxMovementClose = config["maxMovementClose"]
        self.maxSlipClose = config["maxSlipClose"]
        self.minCountOfMarketData = config["minCountOfMarketData"]

        self.slip = 9999.0
        self.signal = 0
        self.countOfMarketData = 0
        self.initialized = 0

    def strategyInit(self, md):
        # 信号模型初始化
        self.plateFactor = plateFactor(md.ask_price, md.bid_price, md.ask_vol, md.bid_vol, self.platePercentPlate,
                                       self.priceTick)
        self.spaceFactor = spaceFactor(md.update_time, md.high_price, md.low_price, md.ask_price, md.bid_price,
                                       self.platePercentSpace, self.priceTick)
        self.initialized = 1

    def upDateFactor(self, md):
        # 更新模型
        self.plateFactor.upDatePlateFactor(md.ask_price, md.bid_price, md.ask_vol, md.bid_vol)
        self.spaceFactor.upDateSpaceFactor(md.ask_price, md.bid_price, md.update_time)
        self.slip = (md.ask_price[0] - md.bid_price[0]) * 10000.0 / (md.ask_price[0] + md.bid_price[0])
        self.signal = self.getSignal()
        self.countOfMarketData += 1

    def getSignal(self):
        # 判断信号
        if self.plateFactor.sizeRatio > self.minSizeRatioOpen - point \
                and self.plateFactor.movement > self.minMovementOpen - point \
                and max(self.spaceFactor.duration, self.spaceFactor.lastDuration) < self.maxDurationOpen + point \
                and self.spaceFactor.space >= 2 and self.spaceFactor.highLowPoint == 1 \
                and self.slip < self.maxSlipOpen + point:
            return 1
        elif self.plateFactor.sizeRatio < self.maxSizeRatioClose + point \
                and self.plateFactor.movement < self.maxMovementClose + point \
                and self.spaceFactor.space <= -1 and self.slip < self.maxSlipClose + point:
            return -1
        else:
            return 0


class spaceFactor:
    # 空间因子模型
    def __init__(self, update_time, high_price, low_price, ask_price, bid_price, platePercent, priceTick):
        # 初始化, 传入日内最高价、最低价
        self.platePercent = platePercent
        self.priceTick = priceTick
        # 将比例转化为价格，比如，100块的股票，千一是一毛钱
        self.price = (ask_price[0] + bid_price[0]) * 0.5
        self.width = math.ceil((ask_price[0] + bid_price[0]) * 0.5 * platePercent / priceTick) * priceTick
        self.high_price = high_price
        self.low_price = low_price
        # 记录上一个数据时间戳
        self.lastTimeStamp = update_time
        self.duration = 0
        self.lastDuration = 0
        self.space = 0
        self.highLowPoint = 0

    def upDateSpaceFactor(self, ask_price, bid_price, update_time):
        # 上涨K线
        if bid_price[0] - self.price > self.width - point:
            # 上一根不是阳线，space为1
            if self.space < point:
                self.space = 1
            # 上一根也是阳线，space+1
            else:
                self.space += 1
            # K线信息
            self.price = bid_price[0]
            # 更新K线时长
            self.lastDuration = self.duration
            self.duration = 0
            # 判断是否破新高
            if self.price > self.high_price + point:
                self.high_price = self.price
                self.highLowPoint = 1
            else:
                self.highLowPoint = 0
        # 下跌K线，逻辑反过来
        elif self.price - ask_price[0] > self.width - point:
            if self.space > -point:
                self.space = -1
            else:
                self.space -= 1
            self.price = ask_price[0]
            self.lastDuration = self.duration
            self.duration = 0
            if self.price < self.low_price - point:
                self.low_price = self.price
                self.highLowPoint = -1
            else:
                self.highLowPoint = 0
        # 剩下的情况，就是空间K线不需要更新
        else:
            # 空间K线的时间增加
            self.duration += millisecondTimeGap(self.lastTimeStamp, update_time)
        self.lastTimeStamp = update_time


class plateFactor:
    # 盘口因子模型
    def __init__(self, ask_price, bid_price, ask_vol, bid_vol, platePercent, priceTick):
        # 初始化
        self.platePercent = platePercent
        self.priceTick = priceTick
        self.width = math.ceil((ask_price[0] + bid_price[0]) * 0.5 * platePercent / priceTick) * priceTick
        self.shortResist = getShortResist(bid_price, bid_vol, self.width)
        self.longResist = getLongResist(ask_price, ask_vol, self.width)
        self.sizeRatio = self.shortResist / self.longResist if self.longResist != 0 else 1
        self.movement = 0
        self.lastAskPrice = ask_price
        self.lastAskVolume = ask_vol
        self.lastBidPrice = bid_price
        self.lastBidVolume = bid_vol

    def upDatePlateFactor(self, ask_price, bid_price, ask_vol, bid_vol):
        # 更新盘口因子
        # 盘口压单比
        self.shortResist = getShortResist(bid_price, bid_vol, self.width)
        self.longResist = getLongResist(ask_price, ask_vol, self.width)
        self.sizeRatio = self.shortResist / self.longResist if self.longResist != 0 else 1
        # 动量因子 = 盘口动量 / 盘口阻力
        tickMovement = getTickMovement(ask_price, bid_price, ask_vol, bid_vol, self.lastAskPrice, self.lastBidPrice,
                                       self.lastAskVolume, self.lastBidVolume, self.width)
        if tickMovement > point and self.longResist:
            self.movement = tickMovement / self.longResist
        elif tickMovement < -point and self.shortResist:
            self.movement = tickMovement / self.shortResist
        else:
            self.movement = 0
        # 记录上一个盘口
        self.lastAskPrice = ask_price
        self.lastAskVolume = ask_vol
        self.lastBidPrice = bid_price
        self.lastBidVolume = bid_vol


class TradingManager:
    # 单股票交易管理
    def __init__(self, config, row):
        # 传入参数文件strategy.json, 个股持仓与标的csv文件的单行
        self.latestMD = None
        self.latest_id = 0
        # 账户信息
        self.account = row['ACCOUNT']
        # 股票代码， symbol为去除后缀的六位数字形成的字符串
        self.stockCode = row["S_INFO_WINDCODE"]
        self.symbol = self.stockCode.split(".")[0]
        # 最小变动价位 * 10000
        self.priceTick = row['PRICE_TICK']
        # 日内最大净头寸
        self.maxNetPosition = row["MAX_NET_POSITION"]
        # 初始化最大可用底仓
        self.totalPosition = row['TOTAL_POSITION']
        # 交易开始与结束时间
        self.startTime = config["startTime"] * 1000
        self.endTime = config["endTime"] * 1000
        # 止损 BP
        self.stopLoss = config['stopLoss'] / 10000
        # 临近涨跌停比例
        self.nearLimitRatio = config["nearLimitRatio"]
        # 挂单超时时限
        self.suspendTimeOut = config["suspendTimeOut"] * 1000
        # 报单异常时限
        self.sendingTimeOut = config["sendingTimeOut"] * 1000
        # 拒单次数上限
        self.rejectTimesLimit = config["rejectTimesLimit"]
        # 临近涨跌停价格
        self.nearHighLimitedPrice = 0
        self.nearLowLimitedPrice = 0
        # 个股控制参数
        self.stopTrading = 0
        # 个股报单异常与撤单异常参数
        self.orderRejectTimes = 0
        self.cancelRejectTimes = 0
        # 敞口参数
        self.netPosition = 0
        self.openPrice = 0
        # 是否已初始化
        self.initialized = 0
        # 获取最小报单价位与交易所信息
        # 交易所
        if self.stockCode.endswith('Z'):
            self.exchange = Exchange.kSzse
        elif self.stockCode.endswith('H'):
            self.exchange = Exchange.kShse
        else:
            self.exchange = None
        # 最小报单单位, 科创版，债券
        if self.stockCode.startswith('68'):
            self.minOrderSize = 200
        elif self.stockCode.startswith('1'):
            self.minOrderSize = 10
        else:
            self.minOrderSize = 100

    def onFirstMarketData(self, md):
        self.nearLowLimitedPrice = md.high_limited * self.nearLimitRatio + md.low_limited * (1 - self.nearLimitRatio)
        self.nearHighLimitedPrice = md.low_limited * self.nearLimitRatio + md.high_limited * (1 - self.nearLimitRatio)
        self.initialized = 1


def getBuyQty(totalPosition, maxNetPosition, netPosition, minOrderSize):
    # 计算可买数量
    buyQty = maxNetPosition - netPosition
    if minOrderSize == 200:
        return min(totalPosition, buyQty)
    else:
        return (min(totalPosition, buyQty) // minOrderSize) * minOrderSize


def getSellQty(netPosition, minOrderSize):
    # 计算可卖数量
    if minOrderSize == 200:
        return netPosition
    else:
        return (netPosition // minOrderSize) * minOrderSize


class PyStrategy(StrategyInterface):

    def __init__(self, server):
        # python和C++接口对象初始化，此行不要改动
        StrategyInterface.__init__(self, server)
        # 自定义参数初始化
        self.__count = 0
        # 订阅列表
        self.stockCodeList = list()
        # 单股票交易管理存在字典中，键为股票symbol。
        self.TradeManagerDict = dict()
        # 单股票信号管理存在字典中，键为股票symbol。
        self.SignalManagerDict = dict()
        # 总控制参数
        self.stopTrading = 0
        self.totalOrderRejectTimes = 0
        self.totalCancelRejectTimes = 0
        self.totalRejectTimesLimit = 0

    def OnInit(self):
        # 实盘与仿真初始化
        self.LogError('读取参数文件, strategy.json')
        with codecs.open('strategy.json', "r", encoding="utf-8") as f:
            # 读取参数配置文件
            config = json.load(f)
            # 读取交易标的文件
            self.LogError('读取交易文件。trade_.csv')
            trade_file = pandas.read_csv('trade_.csv')
            if len(trade_file) == 0:
                self.LogError('交易文件为空，请检查。')
                return False
            else:
                for i in range(len(trade_file)):
                    # 为每个股票建立管理模型
                    stockCode = trade_file.iloc[i]['S_INFO_WINDCODE']
                    priceTick = trade_file.iloc[i]['PRICE_TICK']
                    symbol = stockCode.split('.')[0]
                    self.stockCodeList.append(stockCode)
                    self.TradeManagerDict[symbol] = TradingManager(config, trade_file.iloc[i])
                    self.SignalManagerDict[symbol] = SignalManager(stockCode, symbol, priceTick, config)
                    self.LogError('初始化模型, %s' % stockCode)
            # 总控制参数
            self.totalRejectTimesLimit = config["totalRejectTimesLimit"]
        # 订阅行情，0为OnMarket, 1也订阅OnTrans
        rtn = self.SubStock(self.stockCodeList, 0)
        if rtn:
            self.LogError('行情订阅失败')
            return False
        self.LogError('行情订阅成功')
        return True

    def OnBacktestInit(self, json_obj):
        # 回测专用初始化接口，实盘交易系统不会调用，策略需要在此接口中订阅股票行情
        # 订阅列表
        self.stockCodeList = []
        # 单股票交易管理存在字典中，键为股票symbol。
        self.TradeManagerDict = {}
        # 单股票信号管理存在字典中，键为股票symbol。
        self.SignalManagerDict = {}

        self.LogError('读取参数文件, strategy.json')
        with codecs.open('strategy.json', "r", encoding="utf-8") as f:
            # 读取参数配置文件
            config = json.load(f)
            # 读取交易标的文件
            self.LogError('读取交易文件。trade_.csv')
            trade_file = pandas.read_csv('trade_.csv')
            if len(trade_file) == 0:
                self.LogError('交易文件为空，请检查。')
                return False
            else:
                for i in range(len(trade_file)):
                    # 为每个股票建立管理模型
                    stockCode = trade_file.iloc[i]['S_INFO_WINDCODE']
                    priceTick = trade_file.iloc[i]['PRICE_TICK']
                    symbol = stockCode.split('.')[0]
                    self.stockCodeList.append(stockCode)
                    self.TradeManagerDict[symbol] = TradingManager(config, trade_file.iloc[i])
                    self.SignalManagerDict[symbol] = SignalManager(stockCode, symbol, priceTick, config)
                    self.LogError('初始化模型, %s' % stockCode)
            # 总控制参数
            self.totalRejectTimesLimit = config["totalRejectTimesLimit"]
        # 订阅行情，0为OnMarket, 1也订阅OnTrans
        rtn = self.SubStock(self.stockCodeList, 0)
        if rtn:
            self.LogError('行情订阅失败')
            return False
        self.LogError('行情订阅成功')
        return True

    def OnRelease(self):
        # 策略退出接口。交易系统在安全退出时调用。回测系统在当日结束时调用。
        # self.LogInfo('StrategyPrint: OnRelease()')
        time.sleep(0.1)
        return

    def OnMarketData(self, md):
        # 收到Tick行情
        """
                XyMarketData md 股票快照行情
                    string  symbol                   股票代码
                    int     code                     整型编码股票代码
                    int     status                   状态
                    int     pre_close                前收盘价
                    int     open_price               开盘价
                    int     high_price               最高价
                    int     low_price                最低价
                    int     last_price               最新价
                    int     high_limited             涨停价
                    int     low_limited              跌停价
                    int     volume                   成交量
                    int     turnover                 成交额
                    int     weighted_avg_bid_price   加权平均委买价格
                    int     total_bid_vol            委托买入总量
                    int     weighted_avg_ask_price   加权平均委卖价格
                    int     total_ask_vol            委托卖出总量
                    int     ask_price[0]             申卖价 0-9
                    int     bid_price[0]             申买价 0-9
                    int     ask_vol[0]               申卖量 0-9
                    int     bid_vol[0]               申买量 0-9
                    int     trading_day              交易日
                    int     update_time              时间(HHMMSSmmm)
                    int     num_trades               成交笔数
                    int     iopv                     IOPV净值估值
                    int     yield_to_maturity        到期收益率
                    int     syl1                     市盈率1
                    int     syl2                     市盈率2
                    int     sd2                      升跌2（对比上一笔）
                """
        self.LogInfo('OnMarketData(), symbol: %s' % md.symbol)
        if md.symbol in self.TradeManagerDict:
            # 判断行情是否属于已建立的模型中，每个股票始终记录一个TICK，可用于报单
            self.__count += 1
            tm = self.TradeManagerDict[md.symbol]
            SignalManager = self.SignalManagerDict[md.symbol]
            tm.latestMD = md
            # 交易管理初始化
            if tm.initialized == 0:
                tm.onFirstMarketData(md)
            # 查询所有持仓
            position = self.GetPosition(md.symbol, tm.account)
            tm.netPosition = position.today_buy - position.today_sell
            self.LogInfo('当前净头寸: %s' % tm.netPosition)
            # 获取该股票订单，无订单赋值None
            if tm.latest_id:
                order = self.GetOrder(tm.latest_id)
            else:
                order = None
            # 个股清仓状态或篮子清仓状态
            if tm.stopTrading == 1 or self.stopTrading == 1:
                self.LogInfo('清仓状态, 账户清仓: %s, 个股清仓状态: %s'
                             % (self.stopTrading, tm.stopTrading))
                if tm.netPosition < tm.minOrderSize:
                    # 无头寸
                    if order and (order.status == OrderStatus.kAccepted or order.status == OrderStatus.kPartialFilled):
                        # 订单状态为已报或部成
                        if order.direction == Direction.kBuy:
                            # 多头撤单
                            self.LogError('清仓状态撤单 %s' % order.local_order_id)
                            self.CancelOrder(order.local_order_id, self.__count)

                else:
                    # 有头寸
                    if order and (order.status == OrderStatus.kAccepted or order.status == OrderStatus.kPartialFilled):
                        if order.direction == Direction.kBuy:
                            # 多头撤单， 空头不管
                            self.LogError('清仓状态撤单 %s' % order.local_order_id)
                            self.CancelOrder(order.local_order_id, self.__count)
                    else:
                        # 无委托，报空单
                        self.LogError('清仓状态: 尝试卖出 %s' % tm.netPosition)
                        tm.latest_id = self.sendSellOrder(tm)
            else:
                # 不在清仓状态
                if SignalManager.initialized == 0:
                    # 信号模型未初始化
                    if md.update_time >= 93000000:
                        # 盘前不初始化
                        if md.ask_price[0] > 0 and md.bid_price[0] > 0:
                            # 如果不是涨跌停行情，因子初始化
                            self.LogInfo('模型因子初始化: %s' % md.symbol)
                            SignalManager.strategyInit(md)
                            SignalManager.initialed = 1
                            self.LogInfo('初始化成功: %s' % md.update_time)
                else:
                    # 模型已初始化
                    if md.ask_price[0] == md.low_limited:
                        # 情况1：遭遇跌停, 个股清仓
                        self.LogError('跌停,进入清仓状态: %s, %s' % (md.update_time, md.symbol))
                        tm.stopTrading = 1
                        if tm.netPosition >= tm.minOrderSize:
                            # 有头寸
                            if not order:
                                # 无委托
                                self.LogError('跌停,以跌停价卖出: %s' % tm.netPosition)
                                tm.latest_id = self.sendLowLimitedSellOrder(tm)

                    elif md.bid_price[0] == md.high_limited:
                        # 情况2：遭遇涨停
                        if tm.netPosition >= tm.minOrderSize:
                            # 有头寸
                            if order and (order.status == OrderStatus.kAccepted
                                          or order.status == OrderStatus.kPartialFilled):
                                # 有委托未成交
                                if order.direction == Direction.kBuy:
                                    # 多头撤单
                                    self.LogInfo('涨停多头撤单: %s' % order.local_order_id)
                                    self.CancelOrder(order.local_order_id, self.__count)
                            else:
                                # 无委托
                                self.LogError('涨停,尝试以涨停价卖出: %s' % tm.netPosition)
                                tm.latest_id = self.sendSellOrder(tm)
                        else:
                            # 无头寸, 当天已无交易机会, 个股停止交易
                            self.LogInfo('涨停, 停止个股交易: %s' % md.symbol)
                            tm.stopTrading = 1
                    else:
                        # 情况3：正常行情
                        self.LogInfo('数据积累次数: %s, 当前时间: %s'
                                     % (SignalManager.countOfMarketData, md.update_time))
                        # 更新数据
                        SignalManager.upDateFactor(md)
                        self.LogInfo('因子更新成功, 因子值 sizeRatio: %s, movement: %s,'
                                     % (SignalManager.plateFactor.sizeRatio, SignalManager.plateFactor.movement))
                        self.LogInfo('因子更新成功, 因子值 space: %s, highLowPoint: %s, duration: %s, lastDuration: %s'
                                     % (SignalManager.spaceFactor.space, SignalManager.spaceFactor.highLowPoint,
                                        SignalManager.spaceFactor.duration, SignalManager.spaceFactor.lastDuration))
                        self.LogInfo('因子更新成功, 因子值 slip: %s, signal: %s,'
                                     % (SignalManager.slip, SignalManager.signal))
                        if md.update_time <= tm.startTime \
                                or SignalManager.countOfMarketData <= SignalManager.minCountOfMarketData:
                            # 3.1未至开仓时间或数据不够，忽略。
                            self.LogInfo('最小数据量: %s, 开始交易时间: %s'
                                         % (SignalManager.minCountOfMarketData, tm.startTime))
                        else:
                            # 3.2已积累足够的数据且大于开仓时间
                            # 情况3.2.1：进入了强平阶段，包括下面3种情况
                            if md.update_time > tm.endTime:
                                # ******* 收盘结束时处理仓位
                                self.LogError('收盘清仓, 当前仓位: %s, %s' %
                                              (tm.netPosition, md.symbol))
                                self.stopTrading = 1
                                if tm.netPosition >= tm.minOrderSize:
                                    if order and (order.status == OrderStatus.kAccepted
                                                  or order.status == OrderStatus.kPartialFilled):
                                        # 有委托未成交
                                        if order.direction == Direction.kBuy:
                                            # 多头撤单
                                            self.LogInfo('清仓撤单: %s' % order.local_order_id)
                                            self.CancelOrder(order.local_order_id, self.__count)
                                    else:
                                        # 无委托
                                        self.LogError('收盘卖出, 当前仓位: %s' % tm.netPosition)
                                        tm.latest_id = self.sendSellOrder(tm)
                            elif md.ask_price[0] <= tm.nearLowLimitedPrice:
                                # 3.2.1.2 临近跌停
                                self.LogError('临近跌停清仓, 当前仓位: %s' % tm.netPosition)
                                tm.stopTrading = 1
                                if tm.netPosition >= tm.minOrderSize:
                                    if order and (order.status == OrderStatus.kAccepted
                                                  or order.status == OrderStatus.kPartialFilled):
                                        if order.direction == Direction.kBuy:
                                            self.LogInfo('清仓撤单: %s' % order.local_order_id)
                                            self.CancelOrder(order.local_order_id, self.__count)
                                    else:
                                        self.LogError('临近跌停卖出, 当前仓位: %s' % tm.netPosition)
                                        tm.latest_id = self.sendSellOrder(tm)

                            elif tm.netPosition >= tm.minOrderSize \
                                    and (tm.openPrice - md.ask_price[0]) / (tm.openPrice + point) > tm.stopLoss:
                                # 3.2.1.3 触发止损
                                self.LogError('触发止损清仓, 当前仓位: %s' % tm.netPosition)
                                tm.stopTrading = 1
                                if order and (order.status == OrderStatus.kAccepted
                                              or order.status == OrderStatus.kPartialFilled):
                                    if order.direction == Direction.kBuy:
                                        self.LogInfo('清仓撤单: %s' % order.local_order_id)
                                        self.CancelOrder(order.local_order_id, self.__count)
                                else:
                                    self.LogError('止损卖出, 当前仓位: %s' % tm.netPosition)
                                    tm.latest_id = self.sendSellOrder(tm)

                            elif md.bid_price[0] >= tm.nearHighLimitedPrice:
                                # 情况3.2.2：临近涨停, 只考虑平仓
                                self.LogInfo('临近涨停,不再开仓,当前价: %s, 临近阈值: %s'
                                             % (md.bid_price[0], tm.nearHighLimitedPrice))
                                if SignalManager.signal == -1:
                                    # 有空头信号
                                    if tm.netPosition >= tm.minOrderSize:
                                        if order and (order.status == OrderStatus.kAccepted
                                                      or order.status == OrderStatus.kPartialFilled):
                                            self.LogInfo('空头信号撤单: %s' % order.local_order_id)
                                            self.CancelOrder(order.local_order_id, self.__count)
                                        else:
                                            self.LogInfo('空头信号报单: %s' % tm.netPosition)
                                            tm.latest_id = self.sendSellOrder(tm)

                            else:
                                # 情况3.2.3：正常逻辑
                                if SignalManager.signal == 1:
                                    # 有多头信号
                                    self.LogInfo('多头信号: %s' % md.update_time)
                                    if order and (order.status == OrderStatus.kAccepted
                                                  or order.status == OrderStatus.kPartialFilled):
                                        # 有挂单,不论多空,撤单
                                        self.LogInfo('多头信号撤单: %s' % order.local_order_id)
                                        self.CancelOrder(order.local_order_id, self.__count)
                                    else:
                                        # 无挂单
                                        if tm.maxNetPosition - tm.netPosition >= tm.minOrderSize \
                                                and tm.totalPosition >= tm.minOrderSize:
                                            # 持仓够, 可以开仓
                                            self.LogInfo('多头信号报单: %s' % md.update_time)
                                            tm.latest_id = self.sendBuyOrder(tm)

                                elif SignalManager.signal == -1:
                                    # 有空头信号
                                    if tm.netPosition >= tm.minOrderSize:
                                        if order and (order.status == OrderStatus.kAccepted
                                                      or order.status == OrderStatus.kPartialFilled):
                                            self.LogInfo('空头信号撤单: %s' % order.local_order_id)
                                            self.CancelOrder(order.local_order_id, self.__count)
                                        else:
                                            self.LogInfo('空头信号报单: %s' % tm.netPosition)
                                            tm.latest_id = self.sendSellOrder(tm)
                                        
                        if order:
                            # tick结束判断撤单
                            if order.status == OrderStatus.kAccepted or order.status == OrderStatus.kPartialFilled: 
                                pendingTime = millisecondTimeGap(order.time_accept, md.update_time)
                                if md.bid_price[0] > 0 and pendingTime > tm.suspendTimeOut:
                                    # 非跌停超时撤单
                                    self.LogInfo('非跌停超时撤单: %s' % order.local_order_id)
                                    self.CancelOrder(order.local_order_id, self.__count)
                            
                            elif order.status == OrderStatus.kPendingNew or order.status == OrderStatus.kPendingCancel:
                                # 正报正撤状态异常
                                sendingTime = millisecondTimeGap(order.time_accept, md.update_time)
                                self.LogError('正报正撤状态: %s 毫秒' % sendingTime)
                                if sendingTime > tm.sendingTimeOut:
                                    self.LogError('正报正撤状态大于: %s 毫秒, 异常清仓: %s'
                                                  % (tm.sendingTimeOut, order.local_order_id))
                                    self.stopTrading = 1

    def OnTrans(self, trans_md):
        return

    def OnOrder(self, order_md):
        return

    def OnIndex(self, index_md):
        return

    def OnOrderAccept(self, accept, req_id):
        # 收到报单接受回报
        """
        RspAcceptedField accept 报单接受响应
            string     account          资金账号
            string     symbol           股票代码
            string     entrust_no       系统报单编号
            int        local_order_id   本地报单编号
            int        price            委托价格
            int        qty              委托数量
            int        accepted_qty     报单接受量
            int        remote_order_id  易迅柜台报单编号
            int        accept_time      系统订单时间
            Direction  direction        买卖方向
            OpenClose  open_close       开平方向
            AcceptFrom accept_from      回报来源
        """
        self.LogInfo('OnOrderAccept, order_id: %s, req_id: %s, symbol: %s' %
                     (accept.local_order_id, req_id, accept.symbol))
        self.totalOrderRejectTimes = 0
        if accept.symbol in self.TradeManagerDict:
            tm = self.TradeManagerDict[accept.symbol]
            tm.orderRejectTimes = 0

        return

    def OnOrderReject(self, reject, req_id):
        # 收到报单拒绝回报
        """
        RspRejectedField reject 报单拒绝响应
            string  account         资金账号
            string  symbol          股票代码
            string  error_msg       错误消息
            int     local_order_id  本地报单编号
            int     price           委托价格
            int     qty             委托数量
            int     rejected_qty    报单拒绝量
            int     error_code      错误码
            Direction direction     买卖方向
            OpenClose open_close    开平方向
        """
        self.LogInfo('OnOrderReject, order_id: %s, req_id: %s, symbol: %s' %
                     (reject.local_order_id, req_id, reject.symbol))
        self.totalOrderRejectTimes += 1
        if self.totalOrderRejectTimes > self.totalRejectTimesLimit:
            self.LogError('报单拒绝量超过: %s ,进入清仓状态: %s' % (self.totalRejectTimesLimit, reject.account))
            self.stopTrading = 1

        if reject.symbol in self.TradeManagerDict:
            tm = self.TradeManagerDict[reject.symbol]
            tm.orderRejectTimes += 1
            if tm.orderRejectTimes > tm.rejectTimesLimit:
                self.LogError('报单拒绝量超过: %s ,个股清仓状态: %s'
                              % (tm.rejectTimesLimit, reject.symbol))
                tm.stopTrading = 1
        return

    def OnTrade(self, trade, req_id):
        # 收到订单成交回报
        """
        RspExecutionField trade 成交响应
            string  account              资金账号
            string  symbol               股票代码
            string  entrust_no           系统报单编号
            string  trade_id             成交编号
            int     local_order_id       本地报单编号
            int     price                委托价格
            int     trade_price          成交价格
            int     qty                  委托数量
            int     trade_qty            成交数量
            int     remote_order_id      易迅柜台报单编号
            int     time                 成交时间
            Direction  direction         买卖方向
            OpenClose  open_close        开平方向
            MatchingType  matching_type  撮合类型
        """
        self.LogInfo('OnTrade, order_id: %s, req_id: %s, symbol: %s' % (trade.local_order_id, req_id, trade.symbol))
        # 更新头寸
        position = self.GetPosition(trade.symbol, trade.account)
        if trade.symbol in self.TradeManagerDict:
            tm = self.TradeManagerDict[trade.symbol]
            tm.netPosition = position.today_buy - position.today_sell
            # 记录最后买单的价格， 更新可用底仓
            if trade.direction == Direction.kBuy:
                tm.openPrice = trade.trade_price
                tm.totalPosition -= trade.trade_qty
            # 如果已无头寸，重置买价
            else:
                if tm.netPosition < tm.minOrderSize:
                    tm.openPrice = 0
            # 已无仓位且无底仓
            if tm.netPosition < tm.minOrderSize and tm.totalPosition < tm.minOrderSize:
                self.LogError('已无仓位且无底仓, 停止交易, %s' % trade.symbol)
                tm.stopTrading = 1
            # 清仓完毕
            if tm.netPosition == 0 and tm.stopTrading == 1:
                self.LogError('清仓完毕: %s' % trade.symbol)
        return

    def OnCancelAccept(self, cxl_accept, req_id):
        # 收到撤单接受回报
        """
        RspCancelAcceptedField cxl_accept 撤单接受响应
            string  account          资金账号
            string  symbol           股票代码
            string  entrust_no       系统报单编号
            int     local_order_id   本地报单编号
            int     price            委托价格
            int     qty              委托数量
            int     cancel_qty       撤单数量
            Direction  direction     买卖方向
            OpenClose  open_close    开平方向
        """
        self.LogInfo('OnCancelAccept, order_id: %s, req_id: %s, symbol: %s' %
                     (cxl_accept.local_order_id, req_id, cxl_accept.symbol))
        # 重置撤单拒绝参数
        self.totalCancelRejectTimes = 0
        if cxl_accept.symbol in self.TradeManagerDict:
            tm = self.TradeManagerDict[cxl_accept.symbol]
            tm.cancelRejectTimes = 0
            order = self.GetOrder(tm.latest_id)
            if order.volume_accept == order.volume_cxl + order.volume_trade + order.volume_reject:
                # 判断该订单已了结
                self.LogInfo('订单完成, order_id: %s, req_id: %s, symbol: %s' %
                     (cxl_accept.local_order_id, req_id, cxl_accept.symbol))
                SignalManager = self.SignalManagerDict[cxl_accept.symbol]
                if tm.latestMD.ask_price[0] == tm.latestMD.low_limited and tm.netPosition >= tm.minOrderSize:
                    # 跌停状态且有头寸
                    self.LogError('跌停状态卖出: %s' % tm.netPosition)
                    tm.latest_id = self.sendLowLimitedSellOrder(tm)
                elif tm.stopTrading == 1 and tm.netPosition >= tm.minOrderSize:
                    # 清仓状态
                    self.LogError('清仓状态卖出: %s' % tm.netPosition)
                    tm.latest_id = self.sendSellOrder(tm)
                elif tm.latestMD.bid_price[0] == tm.latestMD.high_limited and tm.netPosition >= tm.minOrderSize:
                    # 涨停状态
                    self.LogError('涨停状态卖出: %s' % tm.netPosition)
                    tm.latest_id = self.sendSellOrder(tm)
                elif SignalManager.signal == 1 and tm.maxNetPosition - tm.netPosition >= tm.minOrderSize \
                        and tm.totalPosition >= tm.minOrderSize:
                    # 有多头信号
                    self.LogInfo('多头信号报单: %s' % tm.latestMD.update_time)
                    tm.latest_id = self.sendBuyOrder(tm)
                elif SignalManager.signal == -1 and tm.netPosition >= tm.minOrderSize:
                    # 有空头信号
                    self.LogError('空头信号报单: %s' % tm.netPosition)
                    tm.latest_id = self.sendSellOrder(tm)
            else:
                # 撤单接受回报先于成交回报, 此tick忽略，在成交回报中更新状态。
                self.LogError('撤单接受回报数量有误, 股票代码: %s, 资金账号: %s, local_order_id: %s,'
                              % (tm.symbol, tm.account, cxl_accept.latest_id))
                self.LogError('委托数量: %s, 撤单接受量: %s, 成交量: %s, 拒绝量: %s'
                              % (order.volume_accept , order.volume_cxl , order.volume_trade, order.volume_reject))

        return

    def OnCancelReject(self, cxl_reject, req_id):
        # 收到撤单拒绝回报
        """
        RspCancelRejectedField cxl_reject 撤单拒绝响应
            string  account          资金账号
            string  symbol           股票代码
            string  entrust_no       系统报单编号
            string  error_msg        错误信息
            int     local_order_id   本地报单编号
            int     price            委托价格
            int     qty              委托数量
            int     rejection_qty    撤单拒绝数量
            int     error_code       错误代码
            Direction  direction     买卖方向
            OpenClose  open_close    开平方向
        """
        self.LogInfo('OnCancelReject, order_id: %s, req_id: %s， symbol: %s' %
                     (cxl_reject.local_order_id, req_id, cxl_reject.symbol))
        self.totalCancelRejectTimes += 1
        if self.totalCancelRejectTimes > self.totalRejectTimesLimit:
            self.LogError('撤单拒绝量超过: %s ,进入清仓状态, 资金账号: %s'
                          % (self.totalRejectTimesLimit, cxl_reject.account))
            self.stopTrading = 1
        if cxl_reject.symbol in self.TradeManagerDict:
            tm = self.TradeManagerDict[cxl_reject.symbol]
            tm.cancelRejectTimes += 1
            if tm.cancelRejectTimes >= tm.rejectTimesLimit:
                self.LogError('撤单拒绝量超过: %s ,个股进入清仓状态, 股票代码: %s'
                              % (tm.rejectTimesLimit, cxl_reject.symbol))
                tm.stopTrading = 1
        return

    def sendBuyOrder(self, TradeManager):
        # 报买单
        new_order = ReqNewField()
        new_order.account = TradeManager.account
        new_order.symbol = TradeManager.symbol
        new_order.price = TradeManager.latestMD.ask_price[0]
        new_order.qty = getBuyQty(TradeManager.totalPosition, TradeManager.maxNetPosition,
                                  TradeManager.netPosition, TradeManager.minOrderSize)
        new_order.direction = Direction.kBuy
        new_order.exchange = TradeManager.exchange
        new_order.open_close = OpenClose.kOpen
        # 调用报单接口
        return self.NewOrder(new_order, self.__count)

    def sendSellOrder(self, TradeManager):
        # 报卖单
        new_order = ReqNewField()
        new_order.account = TradeManager.account
        new_order.symbol = TradeManager.symbol
        new_order.price = TradeManager.latestMD.bid_price[0]
        new_order.qty = getSellQty(TradeManager.netPosition, TradeManager.minOrderSize)
        new_order.direction = Direction.kSell
        new_order.exchange = TradeManager.exchange
        new_order.open_close = OpenClose.kClose
        return self.NewOrder(new_order, self.__count)

    def sendLowLimitedSellOrder(self, TradeManager):
        # 跌停挂单卖出
        new_order = ReqNewField()
        new_order.account = TradeManager.account
        new_order.symbol = TradeManager.symbol
        new_order.price = TradeManager.latestMD.low_limited
        new_order.qty = getSellQty(TradeManager.netPosition, TradeManager.minOrderSize)
        new_order.direction = Direction.kSell
        new_order.exchange = TradeManager.exchange
        new_order.open_close = OpenClose.kClose
        return self.NewOrder(new_order, self.__count)

    def OnTimer(self):
        return

    def OnSignal(self, bytes_data):
        return

    def NotifyOrderUpdate(self, update, req_id):
        return

    def NotifyPositionUpdate(self, update, req_id):
        return

    def OnCmd(self, cmd_bytes, cmd_len, req_id):
        return

    def OnError(self, rsp, req_id):
        return

    def OnPause(self, req_id):
        return

    def OnResume(self, req_id):
        return

    def OnForceClose(self, req_id):
        return
