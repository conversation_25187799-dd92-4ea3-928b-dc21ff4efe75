# -*- coding: utf-8 -*-
import codecs
import json as readJson
import pandas
from xy_pywrapper import *
import numpy as np
point = 0.000001

def getTime(time):

    time = str(int(time))
    milliSecond = int(time[-3:])
    second = int(time[-5:-3])
    minute = int(time[-7:-5])
    hour = int(time[:-7])

    return milliSecond,second,minute,hour

def timeGap(startTime,endTime):
    milliSecondStart, secondStart, minuteStart, hourStart = getTime(startTime)
    milliSecondEnd, secondEnd, minuteEnd, hourEnd = getTime(endTime)

    milliSecondBreak = 0
    secondBreak = 10
    minuteBreak = 30
    hourBreak = 11

    milliSecondRestart = 0
    secondRestart = 50
    minuteRestart = 59
    hourRestart = 12

    gap1 =(hourBreak -hourStart)*3600 +(minuteBreak-minuteStart)*60 + (secondBreak-secondStart) + 0.001*(milliSecondBreak-milliSecondStart)
    gap2 = (hourEnd-hourRestart) * 3600 + (minuteEnd-minuteRestart) * 60 + (secondEnd-secondRestart) + 0.001*(milliSecondEnd-milliSecondRestart)

    if gap1 > -point and gap2 > -point:

        return 0.25 + max(0,(11 -hourStart)*3600 +(30-minuteStart)*60 + (0-secondStart) + 0.001*(0-milliSecondStart))

    else:
        gap = (hourEnd -hourStart)*3600 +(minuteEnd-minuteStart)*60 + (secondEnd-secondStart) + 0.001*(milliSecondEnd-milliSecondStart)

    return gap

def longResist(askPrice,askVolume,width):

    resist = 0

    for i in range(0,10):

        if askPrice[i] <= askPrice[0] + width and askPrice[i] >0:

            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):

    resist = 0
    for i in range(0, 10):

        if bidPrice[i] >= bidPrice[0] - width:
            resist += bidVolume[i]

    return resist

def getEffectivePrice(priceList):

    i = 1
    while i<= len(priceList):
        if priceList[-i] > 0:
            return priceList[-i]
        i+=1
    return priceList[0]

def getAskPriceLimit(askPrice,lastAskPrice,width):

    maxAsk = getEffectivePrice(askPrice)
    maxLastAsk = getEffectivePrice(lastAskPrice)

    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)

def getBidPriceLimit(bidPrice,lastBidPrice,width):

    minBid = getEffectivePrice(bidPrice)
    minLastBid =getEffectivePrice(lastBidPrice)

    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price,askPrice,askVolume):

    volumeBelow = 0
    for i in range(0,len(askPrice)):

        if askPrice[i] <= price and askPrice[i] > 0:

            volumeBelow += askVolume[i]

    return volumeBelow

def volumeUpPrice(price,bidPrice,bidVolume):

    volumeUp = 0
    for i in range(0, len(bidPrice)):

        if bidPrice[i] >= price and bidPrice[i] > 0:
            volumeUp += bidVolume[i]

    return volumeUp

def tickMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):

    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)
    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)

    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)
    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)

    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)
    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)

    deltaAsk = lastVolumeBelow - volumeBelow
    deltaBid = volumeUp - lastVolumeUp

    return deltaAsk + deltaBid

class plateFactor:

    def __init__(self,platePercent,timeGap):

        self.askPrice = np.zeros(10)
        self.bidPrice = np.zeros(10)
        self.askVolume = np.zeros(10)
        self.bidVolume = np.zeros(10)

        self.lastAskPrice = self.askPrice
        self.lastBidPrice = self.bidPrice
        self.lastAskVolume = self.askVolume
        self.lastBidVolume = self.bidVolume

        self.timeGap = timeGap
        self.lastUpdateTime = 0

        self.platePercent = platePercent
        self.width = 0

        self.longResist = 0
        self.shortResist = 0

        self.sizeRatio = 1
        self.movement = 0
        self.slip = 100

        self.firstMarketData = 1

    def update(self,marketData):

        if self.firstMarketData:

            if timeGap(93000000,marketData.update_time) > 3.0:

                self.askPrice = marketData.ask_price
                self.bidPrice = marketData.bid_price
                self.askVolume = marketData.ask_vol
                self.bidVolume = marketData.bid_vol

                self.lastUpdateTime = marketData.update_time

                price = (self.askPrice[0] + self.bidPrice[0]) * 0.5
                self.width = np.ceil(price * self.platePercent / 100.0) * 100.0
                self.firstMarketData = 0

        else:

            self.askPrice = marketData.ask_price
            self.bidPrice = marketData.bid_price
            self.askVolume = marketData.ask_vol
            self.bidVolume = marketData.bid_vol

            if timeGap(self.lastUpdateTime,marketData.update_time) > self.timeGap:

                self.longResist = longResist(self.askPrice, self.askVolume, self.width)
                self.shortResist = shortResist(self.bidPrice, self.bidVolume, self.width)

                self.sizeRatio = float(self.shortResist)/self.longResist
                movement = tickMovement(self.lastAskPrice, self.lastBidPrice, self.lastAskVolume,self.lastBidVolume,
                                self.askPrice, self.bidPrice, self.askVolume,self.bidVolume, self.width)
                if movement > point:
                    self.movement = float(movement) / self.longResist
                elif movement < - point:
                    self.movement = float(movement) / self.shortResist
                else:
                    self.movement = 0

                self.slip =float(self.askPrice[0]-self.bidPrice[0])/(self.askPrice[0]+self.bidPrice[0])*10000.0

                self.lastUpdateTime = marketData.update_time
                self.lastAskPrice = self.askPrice
                self.lastBidPrice = self.bidPrice
                self.lastAskVolume = self.askVolume
                self.lastBidVolume = self.bidVolume

class spaceFactor:

    def __init__(self,spacePercent,timeGap):

        self.askPrice = np.zeros(10)
        self.bidPrice = np.zeros(10)
        self.askVolume = np.zeros(10)
        self.bidVolume = np.zeros(10)

        self.spacePercent = spacePercent
        self.width = 0

        self.timeGap = timeGap
        self.lastUpdateTime = 0

        self.space = 0
        self.price = 0
        self.duration = 0
        self.lastDuration = 0
        self.highLowPoint = 0
        self.spaceDuration = 0

        self.maxPrice = 0
        self.minPrice = 0

        self.firstMarketData = 1

    def update(self,marketData):

        if self.firstMarketData:

            if timeGap(93000000,marketData.update_time) > 3.0:

                self.askPrice = marketData.ask_price
                self.bidPrice = marketData.bid_price
                self.askVolume = marketData.ask_vol
                self.bidVolume = marketData.bid_vol

                self.lastUpdateTime = marketData.update_time

                price = (self.askPrice[0] + self.bidPrice[0]) * 0.5
                self.width = np.ceil(price * self.spacePercent / 100.0) * 100.0
                self.firstMarketData = 0

                self.maxPrice = marketData.high_price
                self.minPrice = marketData.low_price
                self.price = price

        else:

            self.askPrice = marketData.ask_price
            self.bidPrice = marketData.bid_price
            self.askVolume = marketData.ask_vol
            self.bidVolume = marketData.bid_vol

            if timeGap(self.lastUpdateTime, marketData.update_time) > self.timeGap:

                if self.bidPrice[0] - self.price >= self.width:

                    if self.space <= 0:
                        self.space = 1
                    else:
                        self.space += 1

                    self.price = self.bidPrice[0]
                    self.lastDuration = self.duration
                    self.duration = 0

                    if self.price > self.maxPrice + point:
                        self.maxPrice = self.price
                        self.highLowPoint = 1

                    else:
                        self.highLowPoint = 0

                elif self.price - self.askPrice[0] >= self.width:

                    if self.space >= 0:
                        self.space = -1
                    else:
                        self.space -= 1

                    self.price = self.askPrice[0]
                    self.lastDuration = self.duration
                    self.duration = 0

                    if self.price < self.minPrice - point:
                        self.minPrice = self.price
                        self.highLowPoint = -1
                    else:
                        self.highLowPoint = 0
                else:
                    self.duration += timeGap(self.lastUpdateTime, marketData.update_time)

                self.lastUpdateTime = marketData.update_time
                self.spaceDuration = max(self.duration,self.lastDuration)

class signalCompute:

    def __init__(self, minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort):

        self.minSizeRatio = minSizeRatio
        self.minMovement = minMovement
        self.maxDuration = maxDuration
        self.maxSlipLong = maxSlipLong
        self.maxSizeRatio = maxSizeRatio
        self.maxMovement = maxMovement
        self.maxSlipShort = maxSlipShort
        self.signal = 0

    def update(self,sizeRatio,movement,slip,space,spaceDuration,highLowPoint):

        if sizeRatio > self.minSizeRatio - point  and movement > self.minMovement - point and spaceDuration < self.maxDuration + point \
                and space >= 2 and highLowPoint == 1 and slip < self.maxSlipLong + point:
            self.signal = 1
        elif sizeRatio <self.maxSizeRatio + point and movement < self.maxMovement + point and space <= -1 and\
                slip < self.maxSlipShort + point:
            self.signal = -1
        else:
            self.signal = 0

class marketDataToSignal:

    def __init__(self,minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort,
                 spacePercent,spaceTimeGap,platePercent,plateTimeGap):

        self.signalCompute = signalCompute(minSizeRatio,minMovement,maxDuration,maxSlipLong,maxSizeRatio,maxMovement,maxSlipShort)
        self.space = spaceFactor(spacePercent,spaceTimeGap)
        self.plate = plateFactor(platePercent,plateTimeGap)
        self.signal = 0

    def update(self,marketData):

        self.space.update(marketData)
        space = self.space.space
        spaceDuration = self.space.spaceDuration
        highLowPoint = self.space.highLowPoint

        self.plate.update(marketData)
        sizeRatio = self.plate.sizeRatio
        movement = self.plate.movement
        slip = self.plate.slip

        self.signalCompute.update(sizeRatio,movement,slip,space,spaceDuration,highLowPoint)
        self.signal = self.signalCompute.signal

class signalToOrder:

    def __init__(self,maxNetPosition,maxVolume,stockType,warningLimit,startTime,endTime,quantLimit,openLimit,stopLimit,limitOrderDuration,maxDrawDownRatio,exchangeType):

        #开始交易时间
        self.startTime = startTime
        #清仓时间
        self.endTime = endTime

        #累积接收的有效行情数量
        self.quantOfMarketData = 0
        #开仓前需要至少累积多少笔行情
        self.quantLimit =quantLimit
        #用于计算逼近涨停的价格
        self.openLimit = openLimit
        #逼近涨停价格
        self.openLimitPrice = 0
        #用于计算逼近跌停的价格
        self.stopLimit = stopLimit
        #逼近跌停价格
        self.stopLimitPrice = 0
        #用于计算是否达到止损条件
        self.maxDrawDownRatio = maxDrawDownRatio
        #挂单超时判定
        self.limitOrderDuration = limitOrderDuration

        #记录当前挂着的多头单，已经成交了多少
        self.totalTradeQuantLong = 0
        #记录当前挂着的空头单，已经成交了多少
        self.totalTradeQuantShort = 0

        #最大净持仓
        self.maxNetPosition = maxNetPosition
        #最大可用底仓
        self.maxVolume = maxVolume
        #净持仓
        self.netPosition = 0
        #总成交额
        self.totalVolume = 0

        #开始交易标记
        self.startTrading = 0
        #停止交易，开始清仓标记
        self.stopTrading = 0

        #stockType=0为非科创板，stockType=1为科创板
        self.stockType =stockType
        self.exchangeType = exchangeType

        #用于记录多头挂单信息
        self.longPendingOrder = 0
        self.longLimitPrice = 0
        self.longLimitVolume = 0
        self.longOrderTime = 0

        #用于记录多头正报单信息
        self.longSendingOrder = 0
        self.longSendPrice = 0
        self.longSendVolume = 0

        #空头挂单信息
        self.shortPendingOrder = 0
        self.shortLimitPrice = 0
        self.shortLimitVolume = 0
        self.shortOrderTime = 0

        #空头正报单信息
        self.shortSendingOrder = 0
        self.shortSendPrice = 0
        self.shortSendVolume = 0

        #多头单编号，空头单编号
        self.longOrderId = 0
        self.shortOrderId = 0

        #连续警告次数记录，警告门线
        self.warning = 0
        self.warningLimit = warningLimit

        #记录开仓价格，用于计算止损
        self.openPrice = 0

    #计算当前可平仓位
    def getVolumeShort(self):

        #非创业板
        if self.stockType ==0:
            #净持仓的零股部分
            return int(self.netPosition/100.0)*100
        #创业板
        else:
            #小于200，无仓可平
            if self.netPosition < 200:
                return 0
            #否则，全部净持仓可平
            else:
                return self.netPosition

    #计算当前可开仓位
    def getVolumeLong(self):

        #计算目标净持仓，等于最大净持仓和可用底仓取小
        targetPosition = min(self.maxNetPosition , self.maxVolume - self.totalVolume)

        #非创业板
        if self.stockType == 0:
            #目标持仓减去当前净持仓取整
            return int((targetPosition - self.netPosition) / 100.0) * 100
        #创业板
        else:
            if targetPosition - self.netPosition < 200:
                return 0
            else:
                return targetPosition - self.netPosition

    #逼近跌停的价格计算
    def getStopLimitPrice(self,marketData):

        self.stopLimitPrice = int((self.stopLimit * marketData.low_limited + (1-self.stopLimit) * marketData.high_limited)/100.0)*100

    #逼近涨停的价格计算
    def getOpenLimitPrice(self, marketData):

        self.openLimitPrice = int(
            (self.openLimit * marketData.low_limited + (1 - self.openLimit) * marketData.high_limited) / 100.0) * 100

    #止损标记计算
    def getStopFlag(self,marketData):

        if self.stockType == 0:

            if self.netPosition < 100:

                return 0

        else:

            if self.netPosition < 200:

                return 0

        if self.openPrice > self.maxDrawDownRatio * marketData.last_price:

            return 1

class PyStrategy(StrategyInterface):
    def __init__(self, server):
        # python和C++接口对象初始化，此行不要改动
        StrategyInterface.__init__(self, server)
        self.__count = 0
        self.__account = "fak001"

    def OnInit(self):

        # 订阅列表
        self.stockCodeList = []
        # 单股票交易管理存在字典中，键为股票symbol。
        self.orderManagement = {}
        # 单股票信号管理存在字典中，键为股票symbol。
        self.signalManagement = {}

        self.LogError('读取参数文件, strategy.json')
        with codecs.open('strategy.json', "r", encoding="utf-8") as f:
            # 读取参数配置文件
            config = readJson.load(f)
            minSizeRatio = config["minSizeRatio"]
            minMovement = config["minMovement"]
            maxDuration = config["maxDuration"]
            maxSlipLong = config["maxSlipLong"]
            maxSizeRatio = config["maxSizeRatio"]
            maxMovement = config["maxMovement"]
            maxSlipShort = config["maxSlipShort"]
            spacePercent = config["spacePercent"]
            platePercent = config["platePercent"]
            spaceTimeGap = config["spaceTimeGap"]
            plateTimeGap = config["plateTimeGap"]
            warningLimit = config["warningLimit"]
            startTime = config["startTime"]
            endTime = config["endTime"]
            quantLimit = config["quantLimit"]
            openLimit = config["openLimit"]
            stopLimit = config["stopLimit"]
            limitOrderDuration = config["limitOrderDuration"]
            maxDrawDownRatio = config["maxDrawDownRatio"]

            self.LogError('读取交易文件。trade.csv')
            self.tradeFile = pandas.read_csv('trade.csv')
            if len(self.tradeFile) == 0:
                self.LogError('交易文件为空，请检查。')
                return False
            else:
                for i in range(len(self.tradeFile)):
                    # 为每个股票建立管理模型
                    stockCode = self.tradeFile['stockCode'][i]
                    symbol = stockCode.split('.')[0]
                    self.stockCodeList.append(stockCode)
                    maxNetPosition = self.tradeFile["maxNetPosition"][i]
                    maxVolume = self.tradeFile["maxVolume"][i]
                    stockType = self.tradeFile["stockType"][i]
                    exchangeType = self.tradeFile["exchangeType"][i]
                    self.signalManagement[symbol] = marketDataToSignal(minSizeRatio, minMovement, maxDuration, maxSlipLong, maxSizeRatio,
                                                   maxMovement, maxSlipShort,spacePercent, spaceTimeGap, platePercent, plateTimeGap)
                    self.orderManagement[symbol] = signalToOrder(maxNetPosition, maxVolume, stockType, warningLimit, startTime, endTime,
                                                    quantLimit, openLimit, stopLimit, limitOrderDuration,maxDrawDownRatio,exchangeType)

            # 订阅股票
            rtn = self.SubStock(self.stockCodeList, 1)
            if rtn:
                self.LogError('行情订阅失败')
                return False
            self.LogInfo('init complete')
        return True

    def OnBacktestInit(self,json):

        # 回测专用初始化接口，实盘交易系统不会调用，策略需要在此接口中订阅股票行情

        # 订阅列表
        self.stockCodeList = []
        # 单股票交易管理存在字典中，键为股票symbol。
        self.orderManagement = {}
        # 单股票信号管理存在字典中，键为股票symbol。
        self.signalManagement = {}

        self.LogError('读取参数文件, strategy.json')
        with codecs.open('strategy.json', "r", encoding="utf-8") as f:
            # 读取参数配置文件
            config = readJson.load(f)
            minSizeRatio = config["minSizeRatio"]
            minMovement = config["minMovement"]
            maxDuration = config["maxDuration"]
            maxSlipLong = config["maxSlipLong"]
            maxSizeRatio = config["maxSizeRatio"]
            maxMovement = config["maxMovement"]
            maxSlipShort = config["maxSlipShort"]
            spacePercent = config["spacePercent"]
            platePercent = config["platePercent"]
            spaceTimeGap = config["spaceTimeGap"]
            plateTimeGap = config["plateTimeGap"]
            warningLimit = config["warningLimit"]
            startTime = config["startTime"]
            endTime = config["endTime"]
            quantLimit = config["quantLimit"]
            openLimit = config["openLimit"]
            stopLimit = config["stopLimit"]
            limitOrderDuration = config["limitOrderDuration"]
            maxDrawDownRatio = config["maxDrawDownRatio"]

            self.LogError('读取交易文件。trade.csv')
            self.tradeFile = pandas.read_csv('trade.csv')
            if len(self.tradeFile) == 0:
                self.LogError('交易文件为空，请检查。')
                return False
            else:
                for i in range(len(self.tradeFile)):
                    # 为每个股票建立管理模型
                    stockCode = self.tradeFile['stockCode'][i]
                    symbol = stockCode.split('.')[0]
                    self.stockCodeList.append(stockCode)
                    maxNetPosition = self.tradeFile["maxNetPosition"][i]
                    maxVolume = self.tradeFile["maxVolume"][i]
                    stockType = self.tradeFile["stockType"][i]
                    exchangeType = self.tradeFile["exchangeType"][i]

                    self.signalManagement[symbol] = marketDataToSignal(minSizeRatio, minMovement, maxDuration,
                                                                       maxSlipLong, maxSizeRatio,
                                                                       maxMovement, maxSlipShort, spacePercent,
                                                                       spaceTimeGap, platePercent, plateTimeGap)
                    self.orderManagement[symbol] = signalToOrder(maxNetPosition, maxVolume, stockType, warningLimit,
                                                                 startTime, endTime,
                                                                 quantLimit, openLimit, stopLimit, limitOrderDuration,
                                                                 maxDrawDownRatio,exchangeType)

            # 订阅股票
            rtn = self.SubStock(self.stockCodeList, 1)
            if rtn:
                self.LogError('行情订阅失败')
                return False
            self.LogInfo('init complete')
        return True

    def OnRelease(self):
        # 策略退出接口。交易系统在安全退出时调用。回测系统在当日结束时调用。
        self.LogInfo('StrategyPrint: OnRelease()')

    def OnMarketData(self, marketData):
        # 收到Tick行情
        """
            XyMarketData md 股票快照行情
                string  symbol                   股票代码
                int     status                   状态
                int     pre_close                前收盘价
                int     open_price               开盘价
                int     high_price               最高价
                int     low_price                最低价
                int     last_price               最新价
                int     high_limited             涨停价
                int     low_limited              跌停价
                int     volume                   成交量
                int     turnover                 成交额
                int     weighted_avg_bid_price   加权平均委买价格
                int     total_bid_vol            委托买入总量
                int     weighted_avg_ask_price   加权平均委卖价格
                int     total_ask_vol            委托卖出总量
                int     ask_price[0]             申卖价 0-9
                int     bid_price[0]             申买价 0-9
                int     ask_vol[0]               申卖量 0-9
                int     bid_vol[0]               申买量 0-9
                int     trading_day              交易日
                int     update_time              时间(HHMMSSmmm)
                int     num_trades               成交笔数
                int     iopv                     IOPV净值估值
                int     yield_to_maturity        到期收益率
                int     syl1                     市盈率1
                int     syl2                     市盈率2
                int     sd2                      升跌2（对比上一笔）
        """


        if marketData.update_time <93000000 or (marketData.update_time>113000000 and marketData.update_time<130000000):
            return

        symbol = marketData.symbol

        if symbol not in self.orderManagement:
            self.LogError('收到异常行情')
            return

        # 记录下这笔行情
        self.orderManagement[symbol].lastMarketData = marketData
        #逼近涨停价，我们就不再开仓了，这里是用于初始化这个逼近涨停价的判断阈值的
        #含义是，如果阈值没有被初始化，就初始化这个价格
        if self.orderManagement[symbol].openLimitPrice == 0:

            self.orderManagement[symbol].getOpenLimitPrice(marketData)
            self.LogInfo('开仓上限价格：%d,'%(self.orderManagement[symbol].openLimitPrice))
            self.LogInfo('最高价：%d,最低价：%d'%(marketData.high_price,marketData.low_price))

        #逼近跌停价，我们就打算清仓了，这里对这个逼近跌停价的阈值进行初始化
        if self.orderManagement[symbol].stopLimitPrice == 0:

            self.orderManagement[symbol].getStopLimitPrice(marketData)
            self.LogInfo('清仓上限价格：%d' % (self.orderManagement[symbol].stopLimitPrice))

        #发现跌停
        if marketData.bid_price[0] == 0:

            #进入清仓模式
            self.orderManagement[symbol].stopTrading = 1
            self.LogInfo('stopTrading, time:%d'%(marketData.update_time))

            #计算可平仓位
            volumeShort = self.orderManagement[symbol].getVolumeShort()

            #如果有可平仓位
            if volumeShort > 0:

                #如果当前有空单处于正报状态，警告+1，连续多次警告，直接报警
                if self.orderManagement[symbol].shortSendingOrder == 1:

                    self.orderManagement[symbol].warning +=1

                    if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:

                        self.LogError('无法报单')

                #如果当前有挂着的空单
                elif self.orderManagement[symbol].shortPendingOrder == 1:

                    #如果挂单价不是跌停价或挂单量不等于可平量
                    if self.orderManagement[symbol].shortLimitPrice != marketData.low_limited or self.orderManagement[symbol].shortLimitVolume != volumeShort:

                        #撤单
                        order = self.GetOrder(self.orderManagement[symbol].shortOrderId)
                        self.CancelOrder(order.local_order_id, self.orderManagement[symbol].shortOrderId)

                #如果没有挂着的空单，直接跌停价打板
                else:

                    #报单接口，技术部提供，直接依样画葫芦即可

                    #用于记录本地报单编号
                    self.__count += 1
                    new_order = ReqNewField()
                    new_order.account = self.__account
                    new_order.symbol = marketData.symbol
                    new_order.price = marketData.low_limited
                    new_order.qty = volumeShort
                    new_order.direction = Direction.kSell
                    if self.orderManagement[symbol].exchangeType ==1:
                        new_order.exchange = Exchange.kSzse
                    else:
                        new_order.exchange = Exchange.kShse
                    new_order.open_close = OpenClose.kClose
                    self.NewOrder(new_order, self.__count)

                    #更新空头报单的状态
                    self.orderManagement[symbol].shortOrderId = self.__count
                    self.orderManagement[symbol].shortSendingOrder = 1
                    self.orderManagement[symbol].shortSendPrice = marketData.low_limited
                    self.orderManagement[symbol].shortSendVolume = volumeShort
                    self.orderManagement[symbol].shortOrderTime = marketData.update_time

        #发现涨停
        elif marketData.ask_price[0] == 0:

            #计算可平仓位
            volumeShort = self.orderManagement[symbol].getVolumeShort()

            if volumeShort > 0:

                #如果有空头正报或者空头正挂状态，warning加1，warning过大，报警
                if self.orderManagement[symbol].shortSendingOrder == 1 or self.orderManagement[symbol].shortPendingOrder == 1:

                    self.orderManagement[symbol].warning += 1

                    if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:

                        self.LogError('无法报单')

                #正常状态下，涨停价平仓
                else:

                    self.__count += 1
                    new_order = ReqNewField()
                    new_order.account = self.__account
                    new_order.symbol = marketData.symbol
                    new_order.price = marketData.high_limited
                    new_order.qty = volumeShort
                    new_order.direction = Direction.kSell
                    if self.orderManagement[symbol].exchangeType == 1:
                        new_order.exchange = Exchange.kSzse
                    else:
                        new_order.exchange = Exchange.kShse
                    new_order.open_close = OpenClose.kClose
                    self.NewOrder(new_order, self.__count)

                    self.orderManagement[symbol].shortOrderId = self.__count
                    self.orderManagement[symbol].shortSendingOrder = 1
                    self.orderManagement[symbol].shortSendPrice = marketData.high_limited
                    self.orderManagement[symbol].shortSendVolume = volumeShort
                    self.orderManagement[symbol].shortOrderTime = marketData.update_time

        #正常行情
        else:

            #更新交易信号
            self.signalManagement[symbol].update(marketData)
            #记录下当下的信号值
            signal = self.signalManagement[symbol].signal

            self.LogInfo('symbol:%s,slip:%f,sizeRatio:%f,movement:%f,space:%d,duration:%f,highLowPoint:%d,time:%d'%(symbol,self.signalManagement[symbol].plate.slip,self.signalManagement[symbol].plate.sizeRatio,self.signalManagement[symbol].plate.movement,self.signalManagement[symbol].space.space,self.signalManagement[symbol].space.spaceDuration,self.signalManagement[symbol].space.highLowPoint,marketData.update_time))

            #如果还没有开始交易
            if self.orderManagement[symbol].startTrading == 0:

                #累积行情数量加1
                self.orderManagement[symbol].quantOfMarketData +=1

                #累积行情数量足够且最新行情的时间戳大于开始交易时间，开始交易
                if self.orderManagement[symbol].quantOfMarketData > self.orderManagement[symbol].quantLimit and marketData.update_time >self.orderManagement[symbol].startTime:

                    self.orderManagement[symbol].startTrading = 1
                    self.LogInfo('startTrading')

            #如果已经开始交易
            else:

                #如果还没有进入清仓状态
                if self.orderManagement[symbol].stopTrading == 0:

                    #价格逼近跌停或行情时间逼近收盘或触发止损，进入清仓状态，并退出
                    if marketData.ask_price[0] <= self.orderManagement[symbol].stopLimitPrice or marketData.update_time > \
                        self.orderManagement[symbol].endTime or self.orderManagement[symbol].getStopFlag(marketData):

                        self.orderManagement[symbol].stopTrading = 1
                        self.LogInfo('stopTrading, time:%d'%(marketData.update_time))

                        return

                    #没有交易信号
                    if signal == 0:

                        #空头单处于正报，警告+1，连续警告过多，报警
                        if self.orderManagement[symbol].shortSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #空头单处于正挂
                        elif self.orderManagement[symbol].shortPendingOrder == 1:

                            #判断挂单是否超时，如果超时，撤单
                            if timeGap(self.orderManagement[symbol].shortOrderTime,
                                       marketData.update_time) > self.orderManagement[symbol].limitOrderDuration - point:

                                order = self.GetOrder(self.orderManagement[symbol].shortOrderId)
                                self.CancelOrder(order.local_order_id, self.orderManagement[symbol].shortOrderId)

                        #多头单正报，警告+1，连续警告过多，报警
                        if self.orderManagement[symbol].longSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #多头单正挂，看是否超时，超时撤单
                        elif self.orderManagement[symbol].longPendingOrder == 1:

                            if timeGap(self.orderManagement[symbol].longOrderTime,
                                       marketData.update_time) > self.orderManagement[symbol].limitOrderDuration - point:
                                order = self.GetOrder(self.orderManagement[symbol].longOrderId)
                                self.CancelOrder(order.local_order_id, self.orderManagement[symbol].longOrderId)

                    #空头信号
                    elif signal == -1:

                        #多头单正报状态，警告+1，警告过多，直接报警
                        if self.orderManagement[symbol].longSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #多头单正挂，赶紧撤
                        elif self.orderManagement[symbol].longPendingOrder == 1:

                            order = self.GetOrder(self.orderManagement[symbol].longOrderId)
                            self.CancelOrder(order.local_order_id, self.orderManagement[symbol].longOrderId)

                        #空头单正报，警告+1，警告过多，报警
                        if self.orderManagement[symbol].shortSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #空头单正挂，直接撤，准备追单
                        elif self.orderManagement[symbol].shortPendingOrder == 1:

                            order = self.GetOrder(self.orderManagement[symbol].shortOrderId)
                            self.CancelOrder(order.local_order_id, self.orderManagement[symbol].shortOrderId)

                        #没有空头单
                        else:

                            #计算可平量
                            volumeShort = self.orderManagement[symbol].getVolumeShort()

                            #可平量大于0
                            if volumeShort > 0:

                                #没有多头单
                                if self.orderManagement[symbol].longSendingOrder == 0 and self.orderManagement[symbol].longPendingOrder == 0:

                                    #报空单，打对价
                                    self.__count += 1
                                    new_order = ReqNewField()
                                    new_order.account = self.__account
                                    new_order.symbol = marketData.symbol
                                    new_order.price = marketData.bid_price[0]
                                    new_order.qty = volumeShort
                                    new_order.direction = Direction.kSell
                                    if self.orderManagement[symbol].exchangeType == 1:
                                        new_order.exchange = Exchange.kSzse
                                    else:
                                        new_order.exchange = Exchange.kShse
                                    new_order.open_close = OpenClose.kClose
                                    self.NewOrder(new_order, self.__count)

                                    self.orderManagement[symbol].shortOrderId = self.__count
                                    self.orderManagement[symbol].shortSendingOrder = 1
                                    self.orderManagement[symbol].shortSendPrice = marketData.bid_price[0]
                                    self.orderManagement[symbol].shortSendVolume = volumeShort
                                    self.orderManagement[symbol].shortOrderTime = marketData.update_time

                    #多头信号
                    else:

                        #空头单正报，警告+1，连续警告过多，报警
                        if self.orderManagement[symbol].shortSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #空头单正挂，赶紧撤
                        elif self.orderManagement[symbol].shortPendingOrder == 1:

                            order = self.GetOrder(self.orderManagement[symbol].shortOrderId)
                            self.CancelOrder(order.local_order_id, self.orderManagement[symbol].shortOrderId)

                        #多头单正报，警告+1，连续警告过多，报警
                        if self.orderManagement[symbol].longSendingOrder == 1:

                            self.orderManagement[symbol].warning += 1

                            if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                self.LogError('无法报单')

                        #多头单正挂，撤掉，准备追单
                        elif self.orderManagement[symbol].longPendingOrder == 1:

                            order = self.GetOrder(self.orderManagement[symbol].longOrderId)
                            self.CancelOrder(order.local_order_id, self.orderManagement[symbol].longOrderId)

                        #没有多头单
                        else:

                            #计算可开仓位
                            volumeLong = self.orderManagement[symbol].getVolumeLong()

                            #如果可开仓位大于0
                            if volumeLong > 0:

                                #没有空头单
                                if self.orderManagement[symbol].shortSendingOrder == 0 and self.orderManagement[symbol].shortPendingOrder == 0:

                                    #价格没有逼近涨停
                                    if self.orderManagement[symbol].openLimitPrice > marketData.ask_price[0]:

                                        #打对价开仓
                                        self.LogInfo("准备报单，时间：%d" % (marketData.update_time))
                                        self.__count += 1
                                        new_order = ReqNewField()
                                        new_order.account = self.__account
                                        new_order.symbol = marketData.symbol
                                        new_order.price = marketData.ask_price[0]
                                        new_order.qty = volumeLong
                                        new_order.direction = Direction.kBuy
                                        if self.orderManagement[symbol].exchangeType == 1:
                                            new_order.exchange = Exchange.kSzse
                                        else:
                                            new_order.exchange = Exchange.kShse
                                        new_order.open_close = OpenClose.kOpen
                                        self.NewOrder(new_order, self.__count)

                                        self.orderManagement[symbol].longOrderId = self.__count
                                        self.orderManagement[symbol].longSendingOrder = 1
                                        self.orderManagement[symbol].longSendPrice = marketData.bid_price[0]
                                        self.orderManagement[symbol].longSendVolume = volumeLong
                                        self.orderManagement[symbol].longOrderTime = marketData.update_time
                #进入清仓阶段
                else:

                    #处理多单正报
                    if self.orderManagement[symbol].longSendingOrder == 1:

                        self.orderManagement[symbol].warning += 1

                        if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                            self.LogError('无法报单')

                    #多单正挂，撤单
                    elif self.orderManagement[symbol].longPendingOrder == 1:

                        order = self.GetOrder(self.orderManagement[symbol].longOrderId)
                        self.CancelOrder(order.local_order_id, self.orderManagement[symbol].longOrderId)

                    #没有多单
                    else:

                        #计算可平空单
                        volumeShort = self.orderManagement[symbol].getVolumeShort()

                        if volumeShort > 0:

                            #处理空单正报
                            if self.orderManagement[symbol].shortSendingOrder == 1:

                                self.orderManagement[symbol].warning += 1

                                if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
                                    self.LogError('无法报单')

                            #空单正挂，如果超时了，撤单
                            elif self.orderManagement[symbol].shortPendingOrder == 1:

                                if timeGap(self.orderManagement[symbol].shortOrderTime,marketData.update_time) >self.orderManagement[symbol].limitOrderDuration - point:
                                    order = self.GetOrder(self.orderManagement[symbol].shortOrderId)
                                    self.CancelOrder(order.local_order_id, self.orderManagement[symbol].shortOrderId)

                            #没有空头，打对价清仓
                            else:

                                self.__count += 1
                                new_order = ReqNewField()
                                new_order.account = self.__account
                                new_order.symbol = marketData.symbol
                                new_order.price = marketData.bid_price[0]
                                new_order.qty = volumeShort
                                new_order.direction = Direction.kSell
                                if self.orderManagement[symbol].exchangeType == 1:
                                    new_order.exchange = Exchange.kSzse
                                else:
                                    new_order.exchange = Exchange.kShse
                                new_order.open_close = OpenClose.kClose
                                self.NewOrder(new_order, self.__count)

                                self.orderManagement[symbol].shortOrderId = self.__count
                                self.orderManagement[symbol].shortSendingOrder = 1
                                self.orderManagement[symbol].shortSendPrice = marketData.bid_price[0]
                                self.orderManagement[symbol].shortSendVolume = volumeShort
                                self.orderManagement[symbol].shortOrderTime = marketData.update_time

    def OnTrans(self, trans_md):

        return

    def OnOrder(self, order_md):

        return

    def OnIndex(self, index_md):

        return

    def OnOrderAccept(self, accept, req_id):
        # 收到报单接受回报

        """
        RspAcceptedField accept 报单接受响应
            string    account          资金账号
            string    symbol           股票代码
            string    entrust_no       系统报单编号
            int       local_order_id   本地报单编号
            int       price            委托价格
            int       qty              委托数量
            int       accepted_qty     报单接受量
            int       remote_order_id  易迅柜台报单编号
            int       accept_time      系统订单时间
            Direction direction        买卖方向
            OpenClose open_close       开平方向
        """
        self.LogInfo('OnOrderAccept, order_id: %d, req_id: %d' %
                     (accept.local_order_id, req_id))

        symbol = accept.symbol
        if symbol not in self.orderManagement:
            self.LogError("收到异常回报")
            return

        #更新挂单状态，清零警告
        if self.orderManagement[symbol].longOrderId == req_id:
            if self.orderManagement[symbol].longSendingOrder == 1:
                self.orderManagement[symbol].longSendingOrder = 0
                self.orderManagement[symbol].warning = 0
                self.orderManagement[symbol].longPendingOrder = 1
                self.orderManagement[symbol].longLimitPrice = accept.price
                self.orderManagement[symbol].longLimitVolume = accept.qty

        if self.orderManagement[symbol].shortOrderId == req_id:
            if self.orderManagement[symbol].shortSendingOrder == 1:
                self.orderManagement[symbol].shortSendingOrder = 0
                self.orderManagement[symbol].warning = 0
                self.orderManagement[symbol].shortPendingOrder = 1
                self.orderManagement[symbol].shortLimitPrice = accept.price
                self.orderManagement[symbol].shortLimitVolume = accept.qty

        return

    def OnOrderReject(self, reject, req_id):
        # 收到报单拒绝回报
        """
        RspRejectedField reject 报单拒绝响应
            string  account         资金账号
            string  symbol          股票代码
            string  error_msg       错误消息
            int     local_order_id  本地报单编号
            int     price           委托价格
            int     qty             委托数量
            int     rejected_qty    报单拒绝量
            int     error_code      错误码
            Direction direction     买卖方向
            OpenClose open_close    开平方向
        """

        self.LogInfo('OnOrderReject, order_id: %d, req_id: %d' %
                     (reject.local_order_id, req_id))

        symbol = reject.symbol
        if symbol not in self.orderManagement:
            self.LogError("收到异常回报")
            return

        #更新正报状态的订单状态
        if self.orderManagement[symbol].longOrderId == req_id:
            self.orderManagement[symbol].longSendingOrder = 0

        if self.orderManagement[symbol].shortOrderId == req_id:
            self.orderManagement[symbol].shortSendingOrder = 0

        #报警次数加1
        self.orderManagement[symbol].warning += 1

        #连续报警次数过大，则，报警
        if self.orderManagement[symbol].warning > self.orderManagement[symbol].warningLimit:
            self.LogError('无法报单')

        return

    def OnTrade(self, trade, req_id):
        # 收到订单成交回报
        """
        RspExecutionField trade 成交响应
            string  account              资金账号
            string  symbol               股票代码
            string  entrust_no           系统报单编号
            string  trade_id             成交编号
            int     local_order_id       本地报单编号
            int     price                委托价格
            int     trade_price          成交价格
            int     qty                  委托数量
            int     trade_qty            成交数量
            int     remote_order_id      易迅柜台报单编号
            int     time                 成交时间
            Direction  direction         买卖方向
            OpenClose  open_close        开平方向
            MatchingType  matching_type  撮合类型
        """
        symbol = trade.symbol
        if symbol not in self.orderManagement:
            self.LogError("收到异常回报")
            self.LogInfo('OnTrade, order_id: %d, req_id: %d，trade_qty：%d'%
                         (trade.local_order_id, req_id, trade.trade_qty,))
            return

        self.LogInfo('OnTrade, order_id: %d, req_id: %d，trade_qty：%d,update_time:%d' %
                     (trade.local_order_id, req_id,trade.trade_qty,self.orderManagement[symbol].lastMarketData.update_time))

        #多头单成交
        if self.orderManagement[symbol].longOrderId == req_id:
            #多头单已成交量加上本次成交量
            self.orderManagement[symbol].totalTradeQuantLong +=trade.trade_qty

            #如果委托量等于已成交量，即，全部成交
            if trade.qty == self.orderManagement[symbol].totalTradeQuantLong:

                #清除状态
                self.orderManagement[symbol].totalTradeQuantLong = 0
                self.orderManagement[symbol].longPendingOrder = 0
                self.orderManagement[symbol].longSendingOrder = 0
                self.orderManagement[symbol].warning = 0

                #更新净持仓和总成交量
                tradeVolume = trade.qty
                self.orderManagement[symbol].netPosition += tradeVolume
                self.orderManagement[symbol].totalVolume += tradeVolume
                #记录开仓价格
                self.orderManagement[symbol].openPrice = trade.price

        #空头单成交
        if self.orderManagement[symbol].shortOrderId == req_id:
            #空头单已成交量加上本次成交量
            self.orderManagement[symbol].totalTradeQuantShort +=trade.trade_qty
            #委托量等于已成交量，即，全部成交
            if trade.qty == self.orderManagement[symbol].totalTradeQuantShort:
                #清空状态
                self.orderManagement[symbol].totalTradeQuantShort = 0
                self.orderManagement[symbol].shortPendingOrder = 0
                self.orderManagement[symbol].shortSendingOrder = 0
                self.orderManagement[symbol].warning = 0
                #更新净持仓
                tradeVolume = trade.qty
                self.orderManagement[symbol].netPosition -= tradeVolume
        return

    def OnCancelAccept(self, cxl_accept, req_id):
        # 收到撤单接受回报
        """
            RspCancelAcceptedField cxl_accept 撤单接受响应
                string  account          资金账号
                string  symbol           股票代码
                string  entrust_no       系统报单编号
                int     local_order_id   本地报单编号
                int     price            委托价格
                int     qty              委托数量
                int     cancel_qty       撤单数量
                Direction  direction     买卖方向
                OpenClose  open_close    开平方向
        """
        self.LogInfo('OnCancelAccept, order_id: %d, req_id: %d' %
                     (cxl_accept.local_order_id, req_id))

        symbol = cxl_accept.symbol
        if symbol not in self.orderManagement:
            self.LogError("收到异常回报")
            return

        #多头撤单接受
        if self.orderManagement[symbol].longOrderId == req_id:
            #清空了状态
            self.orderManagement[symbol].totalTradeQuantLong = 0
            self.orderManagement[symbol].longPendingOrder = 0
            self.orderManagement[symbol].longSendingOrder = 0
            self.orderManagement[symbol].warning = 0
            #计算成交量
            tradeVolume = cxl_accept.qty - cxl_accept.cancel_qty
            #更新持仓信息
            self.orderManagement[symbol].netPosition += tradeVolume
            self.orderManagement[symbol].totalVolume += tradeVolume
            #如果有成交，更新开仓价格
            if tradeVolume >0:
                self.orderManagement[symbol].openPrice = cxl_accept.price

        #空头撤单接受
        if self.orderManagement[symbol].shortOrderId == req_id:
            self.orderManagement[symbol].totalTradeQuantShort = 0
            self.orderManagement[symbol].shortPendingOrder = 0
            self.orderManagement[symbol].shortSendingOrder = 0
            self.orderManagement[symbol].warning = 0
            #计算成交量
            tradeVolume = cxl_accept.qty - cxl_accept.cancel_qty
            #更新持仓信息
            self.orderManagement[symbol].netPosition -= tradeVolume

        #读取最新行情
        marketData = self.orderManagement[symbol].lastMarketData

        #跌停情况
        if marketData.bid_price[0] == 0:

            volumeShort = self.orderManagement[symbol].getVolumeShort()
            #有空头可平量，打板
            if volumeShort > 0:

                self.__count += 1
                new_order = ReqNewField()
                new_order.account = self.__account
                new_order.symbol = marketData.symbol
                new_order.price = marketData.low_limited
                new_order.qty = volumeShort
                new_order.direction = Direction.kSell
                if self.orderManagement[symbol].exchangeType == 1:
                    new_order.exchange = Exchange.kSzse
                else:
                    new_order.exchange = Exchange.kShse
                new_order.open_close = OpenClose.kClose
                self.NewOrder(new_order, self.__count)

                self.orderManagement[symbol].shortOrderId = self.__count
                self.orderManagement[symbol].shortSendingOrder = 1
                self.orderManagement[symbol].shortSendPrice = marketData.low_limited
                self.orderManagement[symbol].shortSendVolume = volumeShort
                self.orderManagement[symbol].shortOrderTime = marketData.update_time

        #涨停
        elif marketData.ask_price[0] == 0:

            volumeShort = self.orderManagement[symbol].getVolumeShort()

            if volumeShort > 0:

                self.__count += 1
                new_order = ReqNewField()
                new_order.account = self.__account
                new_order.symbol = marketData.symbol
                new_order.price = marketData.high_limited
                new_order.qty = volumeShort
                new_order.direction = Direction.kSell
                if self.orderManagement[symbol].exchangeType == 1:
                    new_order.exchange = Exchange.kSzse
                else:
                    new_order.exchange = Exchange.kShse
                new_order.open_close = OpenClose.kClose
                self.NewOrder(new_order, self.__count)

                self.orderManagement[symbol].shortOrderId = self.__count
                self.orderManagement[symbol].shortSendingOrder = 1
                self.orderManagement[symbol].shortSendPrice = marketData.high_limited
                self.orderManagement[symbol].shortSendVolume = volumeShort
                self.orderManagement[symbol].shortOrderTime = marketData.update_time

        #正常情况
        else:
            #把信号读过来
            signal = self.signalManagement[symbol].signal

            #正常状态
            if self.orderManagement[symbol].stopTrading == 0:

                #空头信号处理
                if signal == -1:

                    volumeShort = self.orderManagement[symbol].getVolumeShort()

                    if volumeShort > 0:

                        if self.orderManagement[symbol].longSendingOrder == 0 and self.orderManagement[symbol].longPendingOrder == 0:

                            self.__count += 1
                            new_order = ReqNewField()
                            new_order.account = self.__account
                            new_order.symbol = marketData.symbol
                            new_order.price = marketData.bid_price[0]
                            new_order.qty = volumeShort
                            new_order.direction = Direction.kSell
                            if self.orderManagement[symbol].exchangeType == 1:
                                new_order.exchange = Exchange.kSzse
                            else:
                                new_order.exchange = Exchange.kShse
                            new_order.open_close = OpenClose.kClose
                            self.NewOrder(new_order, self.__count)

                            self.orderManagement[symbol].shortOrderId = self.__count
                            self.orderManagement[symbol].shortSendingOrder = 1
                            self.orderManagement[symbol].shortSendPrice = marketData.bid_price[0]
                            self.orderManagement[symbol].shortSendVolume = volumeShort
                            self.orderManagement[symbol].shortOrderTime = marketData.update_time

                #多头信号处理
                elif signal == 1:

                    volumeLong = self.orderManagement[symbol].getVolumeLong()

                    if volumeLong > 0:

                        if self.orderManagement[symbol].shortSendingOrder == 0 and self.orderManagement[symbol].shortPendingOrder == 0:

                            if self.orderManagement[symbol].openLimitPrice > marketData.ask_price[0]:

                                self.__count += 1
                                new_order = ReqNewField()
                                new_order.account = self.__account
                                new_order.symbol = marketData.symbol
                                new_order.price = marketData.ask_price[0]
                                new_order.qty = volumeLong
                                new_order.direction = Direction.kBuy
                                if self.orderManagement[symbol].exchangeType == 1:
                                    new_order.exchange = Exchange.kSzse
                                else:
                                    new_order.exchange = Exchange.kShse
                                new_order.open_close = OpenClose.kOpen
                                self.NewOrder(new_order, self.__count)

                                self.orderManagement[symbol].longOrderId = self.__count
                                self.orderManagement[symbol].longSendingOrder = 1
                                self.orderManagement[symbol].longSendPrice = marketData.bid_price[0]
                                self.orderManagement[symbol].longSendVolume = volumeLong
                                self.orderManagement[symbol].longOrderTime = marketData.update_time
            #清仓状态处理
            else:
                volumeShort = self.orderManagement[symbol].getVolumeShort()

                if volumeShort > 0:
                    self.__count += 1
                    new_order = ReqNewField()
                    new_order.account = self.__account
                    new_order.symbol = marketData.symbol
                    new_order.price = marketData.bid_price[0]
                    new_order.qty = volumeShort
                    new_order.direction = Direction.kSell
                    if self.orderManagement[symbol].exchangeType == 1:
                        new_order.exchange = Exchange.kSzse
                    else:
                        new_order.exchange = Exchange.kShse
                    new_order.open_close = OpenClose.kClose
                    self.NewOrder(new_order, self.__count)

                    self.orderManagement[symbol].shortOrderId = self.__count
                    self.orderManagement[symbol].shortSendingOrder = 1
                    self.orderManagement[symbol].shortSendPrice = marketData.bid_price[0]
                    self.orderManagement[symbol].shortSendVolume = volumeShort
                    self.orderManagement[symbol].shortOrderTime = marketData.update_time

        return

    def OnCancelReject(self, cxl_reject, req_id):
        # 收到撤单拒绝回报

        self.LogInfo('OnCancelReject, order_id: %d, req_id: %d' %
                     (cxl_reject.local_order_id, req_id))
        return

    def OnTimer(self):
        return

    def OnSignal(self, bytes_data):
        return

    def NotifyOrderUpdate(self, update, req_id):
        return

    def NotifyPositionUpdate(self, update, req_id):
        return

    def OnCmd(self, cmd_bytes, cmd_len, req_id):
        self.LogInfo(cmd_bytes)
        return

    def OnError(self, rsp, req_id):
        return

    def OnPause(self, req_id):
        return

    def OnResume(self, req_id):
        return

    def OnForceClose(self, req_id):
        return





























