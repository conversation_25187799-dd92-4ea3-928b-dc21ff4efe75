# -*- coding: utf-8 -*-
'''
Copyright (c) 2016-2023, XYAsset. All rights reserved.

Author: <PERSON><PERSON><PERSON>

Create Date: 2023-02-08 10:59:33

Last Time: 2023-02-27 08:40:06

Last Author: <PERSON><PERSON><PERSON>

Description: T0策略demo, 多开空平
'''

import csv
import json
import math
from enum import Enum
from xy_pywrapper import *

g_point = 0.0000001


class SignalType(Enum):
    ''' 信号类型 '''
    # 无信号
    NO_SIGNAL = 0
    # 多头信号
    LONG = 1
    # 空头信号
    SHORT = -1


class TradeStatus(Enum):
    ''' 交易状态 '''
    # 未开始交易
    BEFORE = -1
    # 交易中
    NORMAL = 0
    # 结束交易
    AFTER = 1


class OperationType(Enum):
    ''' 操作类型 '''
    # 强平
    FORCE_CLOSE = -2
    # 平
    CLOSE = -1
    # 默认无操作
    DEFAULT = 0
    # 开
    OPEN = 1
    # 撤
    CANCEL = 10


def symbol_to_int_code(symbol):
    # 将形如600000.SH的股票代码转为ApolloG2定义的intcode
    if len(symbol) == 9 and symbol[6] == '.' and symbol[7] == 'S':
        market = 1 if symbol[8] == 'Z' else 0
        market |= 0x80
        market <<= 24
        return market | int(symbol[0:6])
    else:
        return 0


def int_time_to_seconds(int_time):
    # 形如93523000的时间转换为今日秒数
    return (int_time // 10000000) * 3600 + (int_time // 100000 % 100) * 60 + (int_time // 1000 % 100)


g_am_open_time = 93000000
g_am_close_time = 113000000
g_pm_open_time = 130000000
g_pm_close_time = 145700000
g_noon_gap_seconds = int_time_to_seconds(
    g_pm_open_time) - int_time_to_seconds(g_am_close_time)


def trading_time_gap(start, end):
    delta = int_time_to_seconds(end) - int_time_to_seconds(start)
    if end >= g_pm_open_time and start <= g_am_close_time:
        delta -= g_noon_gap_seconds
    return delta


class SignalEngine():
    """ 信号计算 """

    def __init__(self, sm_ptr, config_dict, price_tick):
        self.__sm_ptr = sm_ptr
        # 初始化一些参数
        self.__plate_percent_plate = config_dict['plate_percent_plate']
        self.__plate_percent_space = config_dict['plate_percent_space']
        self.__min_size_ratio_open = config_dict['min_size_ratio_open']
        self.__min_movement_open = config_dict['min_movement_open']
        self.__max_slip_open = config_dict['max_slip_open']
        self.__max_duration_open = config_dict['max_duration_open']
        self.__max_size_ratio_close = config_dict['max_size_ratio_close']
        self.__max_movement_close = config_dict['max_movement_close']
        self.__max_slip_close = config_dict['max_slip_close']
        self.__plate_proc = PlateProcessor(
            self.__plate_percent_plate, price_tick)
        self.__space_proc = SpaceProcessor(
            self.__plate_percent_space, price_tick)
        self.__last_md = None

    def CalculateSignal(self, md):
        if not self.__last_md:
            # 第一个行情
            self.__last_md = md
            self.__plate_proc.InitPlateValues(md)
            self.__space_proc.InitSpaceValues(md)
            return SignalType.NO_SIGNAL
        # 更新各个因子计算
        slip = self.__calculate_slip(md)
        size_ratio, movement = self.__plate_proc.UpdatePlateValues(
            md, self.__last_md)
        space, duration, high_low_point = self.__space_proc.UpdateSpaceValues(
            md, self.__last_md)
        self.__sm_ptr.stg_ptr.LogInfo('股票代码: %s, size_ratio: %.4f, movement: %.4f, space: %d, slip: %.4f, duration: %d, high_low_point: %d' % (
            md.symbol, size_ratio, movement, space, slip, duration, high_low_point))
        # 更新last_md
        self.__last_md = md
        # 多空信号判断
        if size_ratio > self.__min_size_ratio_open - g_point and \
                movement > self.__min_movement_open - g_point and \
                duration <= self.__max_duration_open and space >= 2 and \
                high_low_point == 1 and slip < self.__max_slip_open + g_point:
            return SignalType.LONG
        elif size_ratio < self.__max_size_ratio_close + g_point and \
                movement < self.__max_movement_close + g_point and \
                space <= -1 and slip < self.__max_slip_close + g_point:
            return SignalType.SHORT
        else:
            return SignalType.NO_SIGNAL

    def __calculate_slip(self, md):
        # 注意：所有的价格在ApolloG2交易系统中都乘了1万倍
        return (md.ask_price[0] - md.bid_price[0]) * 10000 / (md.ask_price[0] + md.bid_price[0])


class PlateProcessor():
    ''' 计算plate相关因子 '''

    def __init__(self, plate_percent_plate, price_tick):
        self.__plate_percent_plate = plate_percent_plate
        self.__price_tick = price_tick
        self.size_ratio = 0
        self.movement = 0

    def InitPlateValues(self, first_md):
        self.__last_max_ask = self.__get_effective_price(first_md.ask_price)
        self.__last_min_bid = self.__get_effective_price(first_md.bid_price)

    def UpdatePlateValues(self, md, last_md):
        # size_ratio
        mid_price = (md.ask_price[0] + md.bid_price[0]) / 2
        plate_width = math.ceil(
            mid_price * self.__plate_percent_plate / self.__price_tick) * self.__price_tick
        long_resist = 0
        short_resist = 0
        for i in range(0, 10):
            if md.ask_price[i] > 0 and md.ask_price[i] <= md.ask_price[0] + plate_width:
                long_resist += md.ask_vol[i]
            if md.bid_price[i] >= md.bid_price[0] - plate_width:
                short_resist += md.bid_vol[i]
        if long_resist:
            self.size_ratio = short_resist / long_resist
        # 获取price_limit
        max_ask = self.__get_effective_price(md.ask_price)
        ask_price_limit = min(max_ask, self.__last_max_ask, max(
            md.ask_price[0], last_md.ask_price[0]) + plate_width)
        min_bid = self.__get_effective_price(md.bid_price)
        bid_price_limit = max(min_bid, self.__last_min_bid, min(
            md.bid_price[0], last_md.bid_price[0]) - plate_width)
        # 获取volume
        volume_below = self.__volume_below_price(
            ask_price_limit, md.ask_price, md.ask_vol)
        last_volume_below = self.__volume_below_price(
            ask_price_limit, last_md.ask_price, last_md.ask_vol)
        volume_up = self.__volume_up_price(
            bid_price_limit, md.bid_price, md.bid_vol)
        last_volume_up = self.__volume_up_price(
            bid_price_limit, last_md.bid_price, last_md.bid_vol)
        # 计算movement
        delta_ask = last_volume_below - volume_below
        delta_bid = volume_up - last_volume_up
        self.movement = delta_ask + delta_bid
        if self.movement > 0 and long_resist:
            self.movement = self.movement / long_resist
        elif self.movement < 0 and short_resist:
            self.movement = self.movement / short_resist

        # 更新历史数据
        self.__last_max_ask = max_ask
        self.__last_min_bid = min_bid
        return self.size_ratio, self.movement

    def __get_effective_price(self, price_list):
        for price in price_list[::-1]:
            if price > 0:
                return price
        return price_list[0]

    def __volume_below_price(self, ask_price_limit, ask_price, ask_vol):
        volume_below = 0
        for i in range(0, len(ask_price)):
            if ask_price[i] > 0 and ask_price[i] <= ask_price_limit:
                volume_below += ask_vol[i]
            else:
                return volume_below
        return volume_below

    def __volume_up_price(self, bid_price_limit, bid_price, bid_vol):
        volume_up = 0
        for i in range(0, len(bid_price)):
            if bid_price[i] > 0 and bid_price[i] >= bid_price_limit:
                volume_up += bid_vol[i]
            else:
                return volume_up
        return volume_up


class SpaceProcessor():
    ''' 计算space相关因子 '''

    def __init__(self, plate_percent_space, price_tick):
        self.__plate_percent_space = plate_percent_space
        self.__price_tick = price_tick
        self.space = 0
        self.duration = 0
        self.last_duration = 0
        self.high_low_point = 0

    def InitSpaceValues(self, first_md):
        self.__curr_price = (first_md.ask_price[0] + first_md.bid_price[0]) / 2
        self.__max_price = first_md.high_price
        self.__min_price = first_md.low_price

    def UpdateSpaceValues(self, md, last_md):
        mid_price = (md.ask_price[0] + md.bid_price[0]) / 2
        space_width = math.ceil(
            mid_price * self.__plate_percent_space / self.__price_tick) * self.__price_tick
        if md.bid_price[0] - self.__curr_price >= space_width:
            if self.space <= 0:
                self.space = 1
            else:
                self.space += 1
            self.__curr_price = md.bid_price[0]
            self.last_duration = self.duration
            self.duration = 0
            # 新K线价格创新高
            if self.__curr_price > self.__max_price:
                self.__max_price = self.__curr_price
                self.high_low_point = 1
            else:
                self.high_low_point = 0
        elif self.__curr_price - md.ask_price[0] >= space_width:
            if self.space >= 0:
                self.space = -1
            else:
                self.space -= 1
            self.__curr_price = md.ask_price[0]
            self.last_duration = self.duration
            self.duration = 0
            # 新K线价格创新低
            if self.__curr_price < self.__min_price:
                self.__min_price = self.__curr_price
                self.high_low_point = -1
            else:
                self.high_low_point = 0
        else:
            self.duration += trading_time_gap(
                last_md.update_time, md.update_time)

        return self.space, max(self.duration, self.last_duration), self.high_low_point


class SymbolManager():
    """ 个股管理 """

    def __init__(self, stg_ptr, symbol, account, max_position, config):
        self.stg_ptr = stg_ptr
        # 报单需要不带交易所的股票代码
        self.symbol = symbol[0:6]
        self.account = account
        self.exchange = Exchange.kSzse if symbol[8] == 'Z' else Exchange.kShse
        # 股票是否是科创板 TODO: 变为trade.csv中的参数
        self.is_star = True if symbol.startswith('688') else False
        self.is_bond = True if symbol.startswith('1') else False
        self.price_tick = 100 if not self.is_bond else 10
        self.min_order_qty = 10 if self.is_bond else 200 if self.is_star else 100
        # 初始化信号引擎
        self.__signal_engine = SignalEngine(self, config, self.price_tick)
        # 当前信号
        self.signal = SignalType.NO_SIGNAL
        # 记录最新行情
        self.last_md = None
        # 最大头寸
        self.max_position = max_position
        # 头寸记录
        self.net_position = 0
        # 策略报单
        self.available_qty = 0
        # 记录当前挂单信息
        self.curr_order_id = 0
        self.curr_accept_time = 0
        self.curr_open_price = 0
        # 收到报单拒绝回报后error数量+1，大于设定阈值后暂停个股交易
        self.error_count = 0
        self.error_limit = config['error_limit']
        # 挂单超时
        self.order_timeout = config['order_timeout']
        # 止损阈值
        self.loss_limit_ratio = config['loss_limit_ratio']
        # 行情累积数量
        self.md_count = 0
        self.min_md_count = config['min_md_count']
        # 临近涨跌停阈值
        self.high_limit_threshold = 0
        self.low_limit_threshold = 0
        self.near_limit_ratio = config['near_limit_ratio']
        # 临近涨跌停
        self.near_high_limit = False
        self.near_low_limit = False
        # 涨跌停
        self.is_high_limit = False
        self.is_low_limit = False
        # 开始结束交易时间
        self.start_time = config['start_time']
        self.stop_time = config['stop_time']
        # 交易时间状态
        self.status = TradeStatus.BEFORE
        # 策略操作
        self.operation = OperationType.DEFAULT

    def UpdateMarketData(self, md):
        # 判断行情时间
        if md.update_time < g_am_open_time or (md.update_time > g_am_close_time and md.update_time < g_pm_open_time):
            self.stg_ptr.LogInfo('忽略连续竞价前和中午的行情, 当前行情时间: %s' %
                                 (md.update_time))
            self.status = TradeStatus.BEFORE
            return
        elif md.update_time < self.start_time:
            self.status = TradeStatus.BEFORE
        elif md.update_time >= self.stop_time:
            self.status = TradeStatus.AFTER
        else:
            self.status = TradeStatus.NORMAL
        # 更新最新行情
        if not self.md_count:
            near_limit_value = (
                md.high_limited - md.low_limited) * self.near_limit_ratio
            self.high_limit_threshold = md.high_limited - near_limit_value
            self.low_limit_threshold = md.low_limited + near_limit_value
            self.stg_ptr.LogError('股票代码: %s, 涨停价: %s, 跌停价: %s, 临近涨跌停比例设置: %s, 临近涨停价格: %s, 临近跌停价格: %s' %
                                  (md.symbol, md.high_limited, md.low_limited, self.near_limit_ratio, self.high_limit_threshold, self.low_limit_threshold))
        self.last_md = md
        self.md_count += 1
        # 暂时默认行情正确，不判断行情时间与本地时间偏差 TODO: 两个行情之间间隔时间太长告警
        # 计算因子信号
        self.signal = self.__signal_engine.CalculateSignal(md)
        self.stg_ptr.LogInfo('股票代码: %s, signal: %s, position: %d, curr_order_id: %d, error: %d' %
                             (md.symbol, self.signal, self.net_position, self.curr_order_id, self.error_count))
        # 判断涨跌停
        self.is_low_limit = True if md.low_limited == md.ask_price[0] else False
        self.is_high_limit = True if md.high_limited == md.bid_price[0] else False
        # 判断临近涨跌停
        self.near_high_limit = True if md.last_price > self.high_limit_threshold else False
        self.near_low_limit = True if md.last_price < self.low_limit_threshold else False
        return

    def GetTradingOperation(self):
        # 跌停挂单
        if self.is_low_limit and self.curr_order_id:
            curr_order = self.stg_ptr.GetOrder(self.curr_order_id)
            if curr_order.price == self.last_md.low_limited:
                self.operation = OperationType.DEFAULT
                return self.operation
        # 是否强平
        if self.net_position >= self.min_order_qty:
            # 计算本单盈亏
            is_loss_limit = self.curr_open_price - \
                self.last_md.last_price > self.curr_open_price * self.loss_limit_ratio
            if self.status == TradeStatus.AFTER or self.is_high_limit or self.near_low_limit or is_loss_limit:
                self.operation = OperationType.FORCE_CLOSE if not self.curr_order_id else OperationType.CANCEL
                self.stg_ptr.LogError('强平操作, 股票代码: %s, 交易状态: %s, 涨停: %s, 临近跌停: %s, 触发止损: %s' %
                                      (self.symbol, self.status, self.is_high_limit, self.near_low_limit, is_loss_limit))
                return self.operation
        # 是否停止交易
        if self.near_high_limit and self.signal == SignalType.SHORT:
            # 特殊情况，临近涨停不能开只能平
            self.operation = OperationType.CLOSE if not self.curr_order_id else OperationType.CANCEL
            return self.operation
        elif self.status != TradeStatus.NORMAL or self.near_low_limit or self.near_high_limit or (self.md_count < self.min_md_count) or (self.error_count > self.error_limit):
            self.operation = OperationType.DEFAULT
            self.stg_ptr.LogInfo('暂停交易, 股票代码: %s, near_low: %s, near_high: %s, md_count: %d, error: %s' % (
                self.symbol, self.near_low_limit, self.near_high_limit, self.md_count, self.error_count))
            return self.operation
        # 是否开平
        if self.signal != SignalType.NO_SIGNAL:
            if not self.curr_order_id:
                self.available_qty = self.GetAvailableQty()
                if self.available_qty:
                    self.operation = OperationType.OPEN if self.signal == SignalType.LONG else OperationType.CLOSE
                    return self.operation
                else:
                    self.stg_ptr.LogError('有信号但是报单数量不足1手, 股票代码: %s, 净头寸: %d, 最大净头寸: %d, 可报股数: %d' %
                                          (self.symbol, self.net_position, self.max_position, self.available_qty))
            else:
                # 有挂单
                self.operation = OperationType.CANCEL
                return self.operation
        else:
            # 没有信号，超时撤单
            if self.curr_order_id and self.curr_accept_time:
                if trading_time_gap(self.curr_accept_time, self.last_md.update_time) > self.order_timeout:
                    self.operation = OperationType.CANCEL
                    return self.operation
        self.operation = OperationType.DEFAULT
        return self.operation

    def GetAvailableQty(self):
        curr_position = self.stg_ptr.GetPosition(self.symbol, self.account)
        if curr_position.freeze_buy or curr_position.freeze_sell:
            self.stg_ptr.LogError('冻结量不为0, 禁止报单, 股票代码: %s, 买入冻结: %s, 卖出冻结: %s' % (
                self.symbol, curr_position.freeze_buy, curr_position.freeze_sell))
            return 0
        if self.signal == SignalType.LONG:
            available_position = curr_position.initial_position - curr_position.today_buy
            qty = min(available_position,
                      self.max_position - self.net_position)
        else:
            qty = self.net_position
        if qty < self.min_order_qty:
            return 0
        if not self.is_star:
            qty = math.floor(qty / self.min_order_qty) * self.min_order_qty
        return qty


class PyStrategy(StrategyInterface):
    """ ApolloG2股票交易系统策略基础框架 """
    # LogInfo可以将日志输出到交易系统日志中
    # LogError可以将日志输出到交易系统日志中，并被推送到风控客户端

    def __init__(self, server):
        # python和C++接口对象初始化，此行不要改动
        StrategyInterface.__init__(self, server)

        # 自定义参数初始化
        # 个股管理字典
        self.__stock_dict = dict()

    def OnInit(self):
        # 实盘交易系统初始化接口，系统启动准备完成后立即调用。回测不会调用
        """
        int SubStock(symbols, subtype)  订阅行情
        @param symbols: 合约代码的列表
        @param subtype: 订阅类型，tick默认订阅，subtype=1订阅trans
                        subtype=2订阅order，subtype=3全部订阅
        @param return: 返回错误编码，0为订阅成功
        """
        # 加载配置 strategy.json
        with open('strategy.json') as config_file:
            config_dict = json.load(config_file)
            # 加载股票列表 trade.csv
            with open('trade.csv') as trade_file:
                trade_list = csv.DictReader(trade_file)
                stock_list = list()
                for row in trade_list:
                    curr_symbol = row['SYMBOL']
                    stock_list.append(curr_symbol)
                    # 初始化个股管理
                    code = symbol_to_int_code(curr_symbol)
                    if not code:
                        self.LogError(
                            '股票代码不正确, 请检查配置文件trade.csv: %s' % (curr_symbol))
                        return False
                    sm = SymbolManager(self, curr_symbol, row['ACCOUNT'],
                                       int(row['MAX_POSITION']), config_dict)
                    self.__stock_dict[code] = sm
                    self.LogInfo('初始化股票代码: %s, 资金账号: %s, 最大头寸: %d, code: %d,  ' %
                                 (curr_symbol, sm.account, sm.max_position, code))
                # 订阅股票行情
                rtn = self.SubStock(stock_list, 0)
                if rtn:
                    self.LogError('行情订阅失败')
                    return False
        return True

    def OnBacktestInit(self, json_str):
        # 回测专用初始化接口，实盘交易系统不会调用，策略需要在此接口中订阅股票行情
        json_obj = json.loads(json_str)
        trading_day = json_obj['trading_day']
        self.LogInfo('回测日期: %d' % (trading_day))
        # 加载配置 strategy.json
        with open('strategy.json') as config_file:
            config_dict = json.load(config_file)
            # 加载股票列表 trade.csv
            with open('trade.csv') as trade_file:
                trade_list = csv.DictReader(trade_file)
                stock_list = list()
                for row in trade_list:
                    curr_symbol = row['SYMBOL']
                    stock_list.append(curr_symbol)
                    # 初始化个股管理
                    code = symbol_to_int_code(curr_symbol)
                    if not code:
                        self.LogError('股票代码不正确, 请检查配置文件trade.csv: %s' %
                                      (curr_symbol))
                        return False
                    sm = SymbolManager(self, curr_symbol, row['ACCOUNT'],
                                       int(row['MAX_POSITION']), config_dict)
                    self.__stock_dict[code] = sm
                    self.LogInfo('初始化股票代码: %s, 资金账号: %s, 最大头寸: %d, code: %d,  ' %
                                 (curr_symbol, sm.account, sm.max_position, code))
                # 订阅股票行情
                rtn = self.SubStock(stock_list, 0)
                if rtn:
                    self.LogError('行情订阅失败')
                    return False
        return True

    def OnRelease(self):
        # 策略退出接口。交易系统在安全退出时调用。回测系统在当日结束时调用。
        return

    def OnMarketData(self, md):
        # 收到Tick行情
        self.LogInfo('OnMarketData(), 股票代码: %s, 最新价: %d, 买一价: %d, 卖一价: %d, 涨停价: %d, 跌停价: %d, 更新时间: %s' %
                     (md.symbol, md.last_price, md.bid_price[0], md.ask_price[0], md.high_limited, md.low_limited, md.update_time))
        """
        XyMarketData md 股票快照行情
            string  symbol                   股票代码
            int     code                     整型编码股票代码
            int     status                   状态
            int     pre_close                前收盘价
            int     open_price               开盘价
            int     high_price               最高价
            int     low_price                最低价
            int     last_price               最新价
            int     high_limited             涨停价
            int     low_limited              跌停价
            int     volume                   成交量
            int     turnover                 成交额
            int     weighted_avg_bid_price   加权平均委买价格
            int     total_bid_vol            委托买入总量
            int     weighted_avg_ask_price   加权平均委卖价格
            int     total_ask_vol            委托卖出总量
            int     ask_price[0]             申卖价 0-9
            int     bid_price[0]             申买价 0-9
            int     ask_vol[0]               申卖量 0-9
            int     bid_vol[0]               申买量 0-9
            int     trading_day              交易日
            int     update_time              时间(HHMMSSmmm)
            int     num_trades               成交笔数
            int     iopv                     IOPV净值估值
            int     yield_to_maturity        到期收益率
            int     syl1                     市盈率1
            int     syl2                     市盈率2
            int     sd2                      升跌2（对比上一笔）
        """
        if md.code in self.__stock_dict:
            sm = self.__stock_dict[md.code]
            # 收到行情，更新信号
            sm.UpdateMarketData(md)
            self.__switch_case(sm.GetTradingOperation(), sm)
        else:
            self.LogError('收到没有订阅的股票行情, 股票代码: %s' % (md.symbol))

    def OnTrans(self, trans_md):
        # 收到逐笔成交
        self.LogInfo('OnTrans(), symbol: %s, update_time: %d' %
                     (trans_md.symbol, trans_md.update_time))
        """
        XyTransData trans_md 股票逐笔成交行情
            string  symbol          股票代码
            int     trading_day     交易日(YYYYMMDD)
            int     update_time     时间(HHMMSSmmm)
            int     index           成交编号
            int     price           成交价格
            int     volume          成交数量
            int     turnover        成交金额
            int     bs_flag         买卖方向(买：'B', 卖：'A', 不明：' ')
            int     ask_order       叫卖方委托序号
            int     bid_order       叫买方委托序号
            char    kind            成交类别
            char    function_code   成交代码
        """
        return

    def OnOrder(self, order_md):
        # 收到逐笔委托
        """
        XyOrderData order_md 股票逐笔委托行情
            string symbol          股票代码
            int    trading_day     交易日(YYYYMMDD)
            int    update_time     时间(HHMMSSmmm)
            int    order           委托号
            int    price           委托价格
            int    volume          委托数量
            int    bs_flag         买卖方向(买：'B', 卖：'S', 不明：' ')
            char   kind            委托类别(上交所 新增委托: 'A', 删除委托: 'D')
            char   function_code   委托代码 v3.0版本弃用
        """
        return

    def OnIndex(self, index_md):
        # 收到股票指数行情
        """
        XyIndexData index_md 股票指数行情
            string  symbol          股票代码
            int     volume          参与计算相应指数的交易数量
            int     turnover        参与计算相应指数的成交金额
            int     open_index      开盘指数
            int     high_index      最高指数
            int     low_index       最低指数
            int     last_index      最新指数
            int     pre_close_index 前收盘指数
            int     trading_day     交易日
            int     update_time     时间(HHMMSSmmm)
        """
        return

    def OnOrderAccept(self, accept, req_id):
        # 收到报单接受回报
        """
        RspAcceptedField accept 报单接受响应
            string     account          资金账号
            string     symbol           股票代码
            string     entrust_no       系统报单编号
            int        local_order_id   本地报单编号
            int        price            委托价格
            int        qty              委托数量
            int        accepted_qty     报单接受量
            int        remote_order_id  易迅柜台报单编号
            int        accept_time      系统订单时间
            Direction  direction        买卖方向
            OpenClose  open_close       开平方向
            AcceptFrom accept_from      回报来源
        """
        self.LogInfo('收到报单接受回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
            accept.symbol, accept.account, accept.local_order_id, req_id))
        if req_id in self.__stock_dict:
            sm = self.__stock_dict[req_id]
            # 获取local_order_id的低20位，高位被用作其他一些标记
            if sm.curr_order_id != (accept.local_order_id & 0xFFFFF):
                # 有可能是撤单回报比成交回报早到
                accept_order = self.GetOrder(accept.local_order_id)
                if accept_order.status == OrderStatus.kFilled or accept_order.status == OrderStatus.kPartialFilled:
                    self.LogError('收到报单接受回报晚于成交回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, curr_order_id: %d, req_id: %d' % (
                        accept.symbol, accept.account, accept.local_order_id, sm.curr_order_id, req_id))
                else:
                    self.LogError('收到报单接受回报但报单号不正确, 股票代码: %s, 资金账号: %s, local_order_id: %d, curr_order_id: %d, req_id: %d' % (
                        accept.symbol, accept.account, accept.local_order_id, sm.curr_order_id, req_id))
        else:
            self.LogError('收到不属于策略的报单接受回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                accept.symbol, accept.account, accept.local_order_id, req_id))
        return

    def OnOrderReject(self, reject, req_id):
        # 收到报单拒绝回报
        """
        RspRejectedField reject 报单拒绝响应
            string  account         资金账号
            string  symbol          股票代码
            string  error_msg       错误消息
            int     local_order_id  本地报单编号
            int     price           委托价格
            int     qty             委托数量
            int     rejected_qty    报单拒绝量
            int     error_code      错误码
            Direction direction     买卖方向
            OpenClose open_close    开平方向
        """
        self.LogError('策略收到报单拒绝回报, 暂停该个股交易, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d, error_msg: %s' %
                      (reject.symbol, reject.account, reject.local_order_id, req_id, reject.error_msg))

        if req_id in self.__stock_dict:
            sm = self.__stock_dict[req_id]
            sm.error_count += 1
            # 获取local_order_id的低20位，高位被用作其他一些标记
            if sm.curr_order_id == (reject.local_order_id & 0xFFFFF):
                sm.curr_order_id = 0
                sm.curr_accept_time = 0
            else:
                self.LogError('收到报单拒绝回报但报单号不正确, 股票代码: %s, 资金账号: %s, local_order_id: %d, curr_order_id: %d, req_id: %d' % (
                    reject.symbol, reject.account, reject.local_order_id, sm.curr_order_id, req_id))
        else:
            self.LogError('收到不属于策略的报单拒绝回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                reject.symbol, reject.account, reject.local_order_id, req_id))
        return

    def OnTrade(self, trade, req_id):
        # 收到订单成交回报
        """
        RspExecutionField trade 成交响应
            string  account              资金账号
            string  symbol               股票代码
            string  entrust_no           系统报单编号
            string  trade_id             成交编号
            int     local_order_id       本地报单编号
            int     price                委托价格
            int     trade_price          成交价格
            int     qty                  委托数量
            int     trade_qty            成交数量
            int     remote_order_id      易迅柜台报单编号
            int     time                 成交时间
            Direction  direction         买卖方向
            OpenClose  open_close        开平方向
            MatchingType  matching_type  撮合类型
        """
        self.LogInfo('收到成交回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
            trade.symbol, trade.account, trade.local_order_id, req_id))

        if req_id in self.__stock_dict:
            sm = self.__stock_dict[req_id]
            # 记录成交量 TODO: kBuy
            if trade.direction == Direction.kBuy:
                sm.net_position += trade.trade_qty
                sm.curr_open_price = trade.price
            else:
                sm.net_position -= trade.trade_qty
            # 获取local_order_id的低20位，高位被用作其他一些标记
            if sm.curr_order_id == (trade.local_order_id & 0xFFFFF):
                curr_order = self.GetOrder(sm.curr_order_id)
                # 判断订单是否完成
                if curr_order.volume == curr_order.volume_trade + curr_order.volume_cxl:
                    sm.curr_order_id = 0
                    sm.curr_accept_time = 0
            else:
                # 有可能是撤单回报比成交回报早到
                self.LogError('收到成交回报但报单号不正确, 股票代码: %s, 资金账号: %s, local_order_id: %d, curr_order_id: %d, req_id: %d' % (
                    trade.symbol, trade.account, trade.local_order_id, sm.curr_order_id, req_id))
        else:
            self.LogError('收到不属于策略的成交回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                trade.symbol, trade.account, trade.local_order_id, req_id))
        return

    def OnCancelAccept(self, cxl_accept, req_id):
        # 收到撤单接受回报
        """
        RspCancelAcceptedField cxl_accept 撤单接受响应
            string  account          资金账号
            string  symbol           股票代码
            string  entrust_no       系统报单编号
            int     local_order_id   本地报单编号
            int     price            委托价格
            int     qty              委托数量
            int     cancel_qty       撤单数量
            Direction  direction     买卖方向
            OpenClose  open_close    开平方向
        """
        self.LogInfo('收到撤单接受回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
            cxl_accept.symbol, cxl_accept.account, cxl_accept.local_order_id, req_id))
        if req_id in self.__stock_dict:
            sm = self.__stock_dict[req_id]
            curr_order = self.GetOrder(sm.curr_order_id)
            # 判断订单是否完成
            if curr_order.volume == curr_order.volume_trade + curr_order.volume_cxl:
                # 订单完成，追单
                sm.curr_order_id = 0
                sm.curr_accept_time = 0
                self.__switch_case(sm.GetTradingOperation(), sm)
            else:
                # 可能是撤单回报早于成交回报，不追单
                self.LogError('收到撤单接受回报但订单没有完成, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                    cxl_accept.symbol, cxl_accept.account, cxl_accept.local_order_id, req_id))
        else:
            self.LogError('收到不属于策略的撤单接受回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                cxl_accept.symbol, cxl_accept.account, cxl_accept.local_order_id, req_id))
        return

    def OnCancelReject(self, cxl_reject, req_id):
        # 收到撤单拒绝回报
        """
        RspCancelRejectedField cxl_reject 撤单拒绝响应
            string  account          资金账号
            string  symbol           股票代码
            string  entrust_no       系统报单编号
            string  error_msg        错误信息
            int     local_order_id   本地报单编号
            int     price            委托价格
            int     qty              委托数量
            int     rejection_qty    撤单拒绝数量
            int     error_code       错误代码
            Direction  direction     买卖方向
            OpenClose  open_close    开平方向
        """
        self.LogError('收到撤单拒绝回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d, error_msg: %s' % (
            cxl_reject.symbol, cxl_reject.account, cxl_reject.local_order_id, req_id, cxl_reject.error_msg))
        # TODO: cxl error_count or not
        if req_id in self.__stock_dict:
            sm = self.__stock_dict[req_id]
            sm.error_count += 1
        else:
            self.LogError('收到不属于策略的撤单拒绝回报, 股票代码: %s, 资金账号: %s, local_order_id: %d, req_id: %d' % (
                cxl_reject.symbol, cxl_reject.account, cxl_reject.local_order_id, req_id))
        return

    def OnTimer(self):
        # 定时器接口
        return

    def OnSignal(self, bytes_data):
        return

    def NotifyOrderUpdate(self, update, req_id):
        return

    def NotifyPositionUpdate(self, update, req_id):
        return

    def OnCmd(self, cmd_bytes, cmd_len, req_id):
        # 人机交互接口
        return

    def OnError(self, rsp, req_id):
        return

    def OnPause(self, req_id):
        return

    def OnResume(self, req_id):
        return

    def OnForceClose(self, req_id):
        return

    def __switch_case(self, operation, sm):
        switcher = {
            OperationType.FORCE_CLOSE: self.__new_order_force_close,
            OperationType.CLOSE: self.__new_order_close,
            OperationType.OPEN: self.__new_order_open,
            OperationType.CANCEL: self.__cancel_order
        }
        func = switcher.get(operation, self.__default_function)
        return func(sm)

    def __new_order_force_close(self, sm):
        # 报单
        new_order = ReqNewField()
        new_order.symbol = sm.symbol
        new_order.account = sm.account
        new_order.exchange = sm.exchange
        new_order.qty = sm.available_qty
        if sm.is_low_limit:
            new_order.price = sm.last_md.ask_price[0]
        else:
            new_order.price = sm.last_md.last_md.bid_price[0]
        new_order.direction = Direction.kSell
        new_order.open_close = OpenClose.kClose
        sm.curr_order_id = self.NewOrder(new_order, sm.last_md.code)
        if sm.curr_order_id:
            sm.curr_accept_time = sm.last_md.update_time
        else:
            sm.error_count += 1

    def __new_order_close(self, sm):
        # 报单
        new_order = ReqNewField()
        new_order.symbol = sm.symbol
        new_order.account = sm.account
        new_order.exchange = sm.exchange
        new_order.qty = sm.available_qty
        new_order.price = sm.last_md.bid_price[0]
        new_order.direction = Direction.kSell
        new_order.open_close = OpenClose.kClose
        sm.curr_order_id = self.NewOrder(new_order, sm.last_md.code)
        if sm.curr_order_id:
            sm.curr_accept_time = sm.last_md.update_time
        else:
            sm.error_count += 1

    def __new_order_open(self, sm):
        # 报单
        new_order = ReqNewField()
        new_order.symbol = sm.symbol
        new_order.account = sm.account
        new_order.exchange = sm.exchange
        new_order.qty = sm.available_qty
        new_order.price = sm.last_md.ask_price[0]
        new_order.direction = Direction.kBuy
        new_order.open_close = OpenClose.kOpen
        sm.curr_order_id = self.NewOrder(new_order, sm.last_md.code)
        if sm.curr_order_id:
            sm.curr_accept_time = sm.last_md.update_time
        else:
            sm.error_count += 1

    def __cancel_order(self, sm):
        # 撤单
        self.CancelOrder(sm.curr_order_id, sm.last_md.code)

    def __default_function(self, sm):
        pass
