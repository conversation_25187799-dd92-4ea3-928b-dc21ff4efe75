# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import signalEstimateTwap as set
import plateSize as ps
import numpy as np
import filedata

def getDailyTest(path_csv,startTime, endTime,platePercent,ratioLowPlateSize,ratioHighPlateSize,tWapDuration,signalDuration):
    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)

    plateSizeResearch = ps.plateSizeResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercent)
    plateSizeResearch.getRationalWidth()#计算宽度
    plateSizeResearch.getResist()#计算每条tick宽度范围内,买单，卖单的和，组成两个对应的列表
    plateSizeResearch.getPlateSizeSignal(ratioLowPlateSize,ratioHighPlateSize)#

    longSignalList = plateSizeResearch.longPlateSizeSignalList#列表，买单量大于卖单量的4倍且小于10000倍，为1，否则为0
    shortSignalList = plateSizeResearch.shortPlateSizeSignalList#列表，卖单量大于买单量的4倍且小于10000倍，为1，否则为0

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    profitListLong = signalEstimate.longSignalEstimateTwap(signalDuration, longSignalList)
    profitListShort = signalEstimate.shortSignalEstimateTwap(signalDuration, shortSignalList)


    return profitListLong, profitListShort

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,ratioLowPlateSize,ratioHighPlateSize,tWapDuration,signalDuration):

    file_list=filedata.path_name(startDate,endDate,stockCode)
    totalProfitListLong = []
    totalProfitListShort = []
    for path_csv in file_list:
        # try:
            profitListLong,profitListShort = getDailyTest(path_csv,startTime, endTime,platePercent,ratioLowPlateSize,ratioHighPlateSize,tWapDuration,signalDuration)
        # except:
        #     continue
        # else:
            for n in profitListLong:
                totalProfitListLong.append(n)
            for n in profitListShort:
                totalProfitListShort.append(n)

    if len(totalProfitListLong) ==0:
        meanLong = 0
    else:
        meanLong = np.mean(totalProfitListLong)

    if len(totalProfitListShort) ==0:
        meanShort = 0
    else:
        meanShort = np.mean(totalProfitListShort)

    return meanLong,len(totalProfitListLong),meanShort,len(totalProfitListShort)

if __name__ == '__main__':

    stockCode = "300274.SZ"

    #开始结束时间
    startTime = 93100
    endTime = 145000

    #盘口压单量的下限和上限
    ratioHighPlateSize = 10000.0
    ratioLowPlateSize = 4.0

    #用于计算有效挂单的宽度
    platePercent = 0.002

    #业绩比较基准，多少秒的tWap
    tWapDuration = 120

    #最小采样间隔，单位是秒
    signalDuration = 60

    #回测的开始结束日期
    startDate = 20190614
    endDate = 20190614


    meanLong, lengthLong, meanShort, lengthShort = getTotalTest(startDate, endDate, stockCode, startTime, endTime,platePercent,
                                                                ratioLowPlateSize, ratioHighPlateSize,tWapDuration, signalDuration)
    print(meanLong, lengthLong, meanShort, lengthShort)
    #-1.2896496289830153 159 17.16468392362287 7