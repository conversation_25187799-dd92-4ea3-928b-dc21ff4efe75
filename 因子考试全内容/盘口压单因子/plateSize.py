import numpy as np
point = 0.000001

def longResist(askPrice,askVolume,width):

    resist = 0

    for i in range(0,10):

        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:

            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):

    resist = 0
    for i in range(0, 10):

        if bidPrice[i] > bidPrice[0] - width - point:
            resist += bidVolume[i]

    return resist

class plateSizeResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):
        '''

        :param askPriceList:
        :param bidPriceList:
        :param askVolumeList:
        :param bidVolumeList:
        :param volumeList:
        :param amountList:
        :param lastPriceList:
        :param timeStampList:
        :param platePercent: 计算宽度
        '''

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.longPlateSizeSignalList = []
        self.shortPlateSizeSignalList = []




    def getRationalWidth(self):
        '''
        更新宽度
        return 0.02
        '''
        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01



    def getResist(self):
        '''
        :return:
        长度4535  均值799.8640485115765 [1306.0，701.0.....]
        长度4535 均值3404.414998897464  [543.0,573.0,564.0,515.0...]

        '''

        for i in range(0, len(self.askPriceList)):
            self.longResistList.append(longResist(self.askPriceList[i], self.askVolumeList[i], self.width))
            self.shortResistList.append(shortResist(self.bidPriceList[i], self.bidVolumeList[i], self.width))



    def getPlateSizeSignal(self,ratioLow,ratioHigh):
        '''
        :return:
        长度4535  和2482
        长度4535  和19
        '''

        for i in range(0, len(self.shortResistList)):

            if self.shortResistList[i] >= self.longResistList[i] * ratioLow and self.shortResistList[i] <= self.longResistList[i] * ratioHigh:
                '''tick买单量之和大于等于卖单量和的4倍，小于10000倍'''

                self.longPlateSizeSignalList.append(1)

            else:
                self.longPlateSizeSignalList.append(0)

            if self.longResistList[i] >= self.shortResistList[i] * ratioLow and self.longResistList[i] <= self.shortResistList[i] * ratioHigh:
                '''tick卖单量之和大于等于买单量和的4倍，小于10000倍'''
                self.shortPlateSizeSignalList.append(1)

            else:
                self.shortPlateSizeSignalList.append(0)

