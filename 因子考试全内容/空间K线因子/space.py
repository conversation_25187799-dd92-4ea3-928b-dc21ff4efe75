# -*- coding: utf-8 -*-
point = 0.000001

def getSpaceList(askPriceList,bidPriceList,width):
    '''
    print(len(spaceList)):4573
    print(sum(spaceList)):-2134
    '''

    spaceList=[]

    price = 0.5*(askPriceList[0][0] + bidPriceList[0][0])
    space = 0

    for i in range(0,len(askPriceList)):
        if bidPriceList[i][0] - price > width - point:
            if space < point:
                space = 1
            else:
                space += 1
            price = bidPriceList[i][0]

        if price -askPriceList[i][0] > width - point:
            if space > -point:
                space = -1
            else:
                space -= 1
            price = askPriceList[i][0]

        spaceList.append(int(space))
    return spaceList

def MovementOrRevert(spaceList,spaceLow,spaceHigh):
    '''
    print(longMovement,longRevert,shortMovement,shortRevert):1 3 6 2
    '''


    longMovement = 0
    longRevert = 0
    shortMovement = 0
    shortRevert = 0

    for i in range(0,len(spaceList)-1):

        if spaceList[i] >= spaceLow and spaceList[i] <= spaceHigh and spaceList[i]>=1:


            if spaceList[i+1] - spaceList[i] ==1:
                longMovement +=1

            if spaceList[i+1] == -1:
                longRevert += 1


        if -spaceList[i] >= spaceLow and -spaceList[i] <= spaceHigh and spaceList[i]<=-1:

            if spaceList[i + 1] - spaceList[i] == -1:
                shortMovement += 1

            if spaceList[i + 1] == 1:
                shortRevert += 1
    return longMovement,longRevert,shortMovement,shortRevert