import pandas as pd
import numpy as np
import filedata

point = 0.000001


#把tick数据转换成列表
def processStockData(dataList,startTime,endTime):
    '''

    :param dataList: 特定交易日，特定股票股票代码对应的tick数据组成的二位数组
    :param startTime: 093100
    :param endTime: 145000
    :return: askPriceList，每条tick卖一到卖十价组成的列表为元素，组成的列表
    bidPriceList，同上
    askVolumeList,同上
    bidVolumeList,同上
    volumeList,当前tick成交总量减去上一条tick成交总量的差，单位为手数，组成的列表
    amountList,当前tick成交金额减去上一条tick成交金额的差，（成交金额除以了100），组成的列表
    lastPriceList,最新价组成的列表
    timeStampList，时间组成的列表
    '''
    # print(dataList[0],startTime,endTime)


    askPriceList = []
    bidPriceList = []
    lastPriceList = []
    volumeList=[]
    amountList = []
    askVolumeList = []
    bidVolumeList = []
    timeStampList = []

    for i in range(0, len(dataList)):#一条一条tick进行处理
        '''wind代码，原始code,业务发生日，交易日，时间，状态，前收盘价，开盘价，最高价，最低价，最新价，申卖价12~21，申卖量22~31，申买价32~41，申买量42~51，成交笔数，成交总量，
        成交总金额，委托买入总量，委托卖出总量，加权平均委买价格，加权平均委卖价格，iopv净值估值，到期收益率，涨停价，跌停价，证券信息前缀，市盈率1，市盈率2，升跌2'''
        # print(i)
        #


        '''时间除以1000'''
        time = dataList[i][4] / 1000.0


        if time > startTime and time < endTime:#时间符合参数设定区间的tick,

            lastPrice = dataList[i][10] / 10000.0 #最新价
            if abs(lastPrice) < point:#如果最新价等于0，这一条tick不再进行处理，直接跳过，即即使时间符合条件，无价格的不处理，涨跌停等，，
                continue
            askPrice = []#一条tick的卖价
            askVolume = []#一条tick的卖量
            bidPrice = []
            bidVolume = []
            for j in range(0,10):#获取10档买价，买量，卖价，卖量
                askPrice.append(dataList[i][11+j]/10000.0)
                askVolume.append(dataList[i][21+j]/100.0)
                bidPrice.append(dataList[i][31+j]/10000.0)
                bidVolume.append(dataList[i][41+j]/100.0)



            if i == 0:#成交总量（股数），成交总金额,获取成交总量和成交总金额减去上一条tick的成交总量和成交金额的值
                volume =dataList[i][52] / 100.0
                amount =dataList[i][53] / 100.0
            else:
                volume =dataList[i][52] / 100.0 - dataList[i - 1][52] / 100.0
                amount =dataList[i][53] / 100.0 - dataList[i - 1][53] / 100.0


            if askPrice[0] == 0 or bidPrice[0] == 0.0:#只要出现一次0，后面数据就不再进行处理，整个的这个csv不再处理
                break

            timeStampList.append(time)
            askPriceList.append(askPrice)
            bidPriceList.append(bidPrice)
            askVolumeList.append(askVolume)
            bidVolumeList.append(bidVolume)
            lastPriceList.append(lastPrice)
            volumeList.append(volume)
            amountList.append(amount)


    return askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList






if __name__ == '__main__':

    stockCode = "000001.SZ"
    date = 20190604
    stockData = pd.read_csv(filedata.path+str(date)+f'_{stockCode}.csv').values
    # print(stockData)
    startTime = 93000
    endTime = 103000
    askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList=processStockData(stockData, startTime, endTime)

    for i in range(200,800):
        print(askPriceList[i],bidPriceList[i],askVolumeList[i],bidVolumeList[i],volumeList[i],amountList[i],lastPriceList[i],timeStampList[i])
