# -*- coding: utf-8 -*-
import pandas as pd
import numpy as np
import processData as pc
import space as sp
import filedata


def getDailyResult(pach_csv,startTime, endTime,percent,spaceLow,spaceHigh):


    stockData = pd.read_csv(pach_csv).values

    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)


    width = np.ceil(lastPriceList[0] * percent/0.01)*0.01
    spaceList = sp.getSpaceList(askPriceList,bidPriceList,width)

    longMovement, longRevert, shortMovement, shortRevert = sp.MovementOrRevert(spaceList,spaceLow,spaceHigh)

    return longMovement, longRevert, shortMovement, shortRevert

def getSingleStockResult(startDate,endDate,stockCode,startTime, endTime,percent,spaceLow,spaceHigh):

    file_list=filedata.path_name(startDate,endDate,stockCode)

    totalMovementLong = 0
    totalRevertLong = 0
    totalMovementShort = 0
    totalRevertShort = 0
    for path_csv in file_list:
        # try:
            movementLong, revertLong, movementShort, revertShort = getDailyResult(path_csv,startTime, endTime,percent,spaceLow,spaceHigh)
        # except:
        #     continue
        # else:
            totalMovementLong += movementLong
            totalRevertLong += revertLong
            totalMovementShort += movementShort
            totalRevertShort += revertShort

    lengthLong = totalMovementLong+totalRevertLong
    if lengthLong == 0:
        longMovementWinRate=0.5
    else:
        longMovementWinRate = totalMovementLong/lengthLong

    lengthShort = totalMovementShort + totalRevertShort
    if lengthShort == 0:
        shortMovementWinRate = 0.5
    else:
        shortMovementWinRate = totalMovementShort / lengthShort

    return longMovementWinRate,lengthLong,shortMovementWinRate,lengthShort

if __name__ == '__main__':

    #股票代码,需要本地文件有数据
    stockCode = "000001.SZ"

    # 统计的开始结束时间
    startTime = 93100
    endTime = 145000


    # 统计的开始结束日期
    startDate = 20190614
    endDate = 20190614

    # 空间因子的上下限区间，闭区间
    spaceHigh = 20000.0
    spaceLow = 2.0

    # 空间K线的宽度
    percent = 0.001

    #(0.25, 4, 0.75, 8)
    print(getSingleStockResult(startDate, endDate, stockCode, startTime, endTime, percent, spaceLow, spaceHigh))