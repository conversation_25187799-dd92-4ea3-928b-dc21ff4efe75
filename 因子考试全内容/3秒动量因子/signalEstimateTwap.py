point = 0.000001
import numpy as np
import timeGap as tg


def longSignalEstimate(askPrceList,bidPriceList,index,lastPriceList,duration,timeStampList):
    '''
    :param askPrceList:
    :param bidPriceList:
    :param index: 满足单量变动比例（加减变动为根据上一tick计算所得，空减加多加，比上空单；空加，多减，相加后，比上多单）
    :param lastPriceList:
    :param duration: 120秒
    :param timeStampList:
    :return: 后120秒内的平均最新价减去满足动量tick买卖均价，比上后120秒内的平均最新价
    '''

    tradePrice = 0.5*(askPrceList[index][0]+bidPriceList[index][0])#按索引取tick卖一买一中间价
    if lastPriceList[index] ==0:
        tWapList = [tradePrice]
    else:
        tWapList = [lastPriceList[index]]

    j = index +1
    while tg.timeGap(timeStampList[index],timeStampList[j]) < duration+point and j <= len(lastPriceList)-2:
        '''时间间隔小于120秒，且没到最后1个tick,超过120秒就跳出循环，不再把价格放入列表'''
        if lastPriceList[j] !=0:
            tWapList.append(lastPriceList[j])
        else:
            tWapList.append(tradePrice)
        j+=1

    tWap = np.mean(tWapList)
    profit = tWap - tradePrice

    return profit/tWap * 10000.0

def shortSignalEstimate(askPrceList,bidPriceList,index,lastPriceList,duration,timeStampList):

    tradePrice =  0.5*(askPrceList[index][0]+bidPriceList[index][0])
    if lastPriceList[index] == 0:
        tWapList = [tradePrice]
    else:
        tWapList = [lastPriceList[index]]

    j = index + 1
    while tg.timeGap(timeStampList[index], timeStampList[j]) < duration + point and j <= len(lastPriceList) - 2:
        if lastPriceList[j] != 0:
            tWapList.append(lastPriceList[j])
        else:
            tWapList.append(tradePrice)
        j += 1

    tWap = np.mean(tWapList)
    profit = tradePrice-tWap

    return profit / tWap * 10000.0

class signalEstimate:

    def __init__(self,askPriceList,bidPriceList,lastPriceList,tWapDuration,timeStampList):
        '''卖十档价，买十档价，最新价，'''

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.lastPriceList = lastPriceList
        self.tWapDuration = tWapDuration
        self.timeStampList = timeStampList

    def longSignalEstimateTwap(self,signalDuration,longSignalList):

        profitList = []
        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            if position == 0:

                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.tWapDuration:
                    '''每条tick进来，计算和组后时间的间隔'''
                    break

                if longSignalList[i] > point:
                    '''空方压单减少，多方单量增加，相加后，比上空方盘口单量，这里的单量都是筛选过的，比例大于阈值4，记为1'''
                    startTime = self.timeStampList[i]#记录tick时间，position更新为1
                    position = 1
                    '''tick买卖均价由于后120秒平均最新价的比例'''
                    profitList.append(longSignalEstimate(self.askPriceList, self.bidPriceList,i, self.lastPriceList, self.tWapDuration, self.timeStampList))
            else:
                '''
                position变为1后，进入else,starttime变为1时的时间,
                60秒后position重新更新为0，进入上面if循环
                '''
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    position = 0

        return profitList

    def shortSignalEstimateTwap(self,signalDuration,shortSignalList):
        # print(signalDuration,shortSignalList)
        # a=input()

        profitList = []
        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            if position == 0:

                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.tWapDuration:
                    break

                if shortSignalList[i] > point:
                    startTime = self.timeStampList[i]
                    position = 1
                    profitList.append(shortSignalEstimate(self.askPriceList, self.bidPriceList, i, self.lastPriceList, self.tWapDuration, self.timeStampList))
            else:
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    position = 0

        return profitList


