point = 0.00001
import numpy as np

def getEffectivePrice(priceList):
    i = 1
    while i<= len(priceList):

        if priceList[-i] > point:
            return priceList[-i]
        i+=1
    return priceList[0]

def getAskPriceLimit(askPrice,lastAskPrice,width):
    maxAsk = getEffectivePrice(askPrice)
    maxLastAsk = getEffectivePrice(lastAskPrice)
    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)

def getBidPriceLimit(bidPrice,lastBidPrice,width):
    minBid = getEffectivePrice(bidPrice)
    minLastBid =getEffectivePrice(lastBidPrice)
    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price,askPrice,askVolume):
    volumeBelow = 0
    for i in range(0,len(askPrice)):
        if askPrice[i] < price + point and askPrice[i] > point:

            volumeBelow += askVolume[i]
    return volumeBelow

def volumeUpPrice(price,bidPrice,bidVolume):
    volumeUp = 0
    for i in range(0, len(bidPrice)):
        if bidPrice[i] > price - point and bidPrice[i] > point:
            volumeUp += bidVolume[i]

    return volumeUp

def threeSecondMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):
    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)
    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)

    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)#前一条价格限制范围内tick卖量的和


    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)#当前tick价格限制范围内tick卖量的和
    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)#前一条价格限制范围内tick买量的和

    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)#当前tick价格限制范围内tick买量的和

    deltaAsk = lastVolumeBelow - volumeBelow#当前tick区间内卖量和减少值
    deltaBid = volumeUp - lastVolumeUp#当前tick区间买量和增加值


    return deltaAsk + deltaBid

def longResist(askPrice,askVolume,width):
    '''不需要重写'''

    resist = 0

    for i in range(0,10):
        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:
            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):
    '''不需要重写'''
    resist = 0
    for i in range(0, 10):
        if bidPrice[i] > bidPrice[0] - width - point:
            resist += bidVolume[i]

    return resist

class plateMovementResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.movementList = [0]

        self.longMovementSignalList = []
        self.shortMovementSignalList = []

    def getRationalWidth(self):
        '''不需要重写'''
        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01

    def getResist(self):
        '''不需要重写'''
        for i in range(0, len(self.askPriceList)):
            self.longResistList.append(longResist(self.askPriceList[i], self.askVolumeList[i], self.width))
            self.shortResistList.append(shortResist(self.bidPriceList[i], self.bidVolumeList[i], self.width))

    def getMovement(self):
        '''
        print(len(self.movementList)):4581   [0,-0.0647887323943662, 0.08570046305895362, 0.0407427353829583, -11.03203661327231, 0.06357156617222115.....]
        print(sum(self.movementList)):-70.27912629484068
        '''

        for i in range(1, len(self.askPriceList)):#从第二条tick开始循环索引
            movement = threeSecondMovement(self.askPriceList[i - 1], self.bidPriceList[i - 1], self.askVolumeList[i - 1], self.bidVolumeList[i - 1],
                                self.askPriceList[i], self.bidPriceList[i], self.askVolumeList[i], self.bidVolumeList[i],self.width)

            if movement > point:
                self.movementList.append(movement/self.longResistList[i])

            elif movement < - point:
                self.movementList.append(movement / self.shortResistList[i])
            else:
                self.movementList.append(0)


    def getMovementSignal(self,ratioLow,ratioHigh):

        for i in range(0, len(self.movementList)):

            if self.movementList[i] >= ratioLow and self.movementList[i] <= ratioHigh :

                self.longMovementSignalList.append(1)
            else:
                self.longMovementSignalList.append(0)

            if -self.movementList[i] >= ratioLow and -self.movementList[i] <=ratioHigh :
                self.shortMovementSignalList.append(1)
            else:
                self.shortMovementSignalList.append(0)






