# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import plate
import space
import slip
import signal
import simpleBackTest as sbt
import numpy as np
import filedata

def getDailyResult(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,minSizeRatioOpen,minMovementOpen,
                   maxDurationOpen,maxSlipOpen,maxSlipClose,maxSizeRatioClose,maxMovementClose,maxPosition):

    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)


    plateResearch = plate.plateResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercentPlate)

    plateResearch.getRationalWidth()
    plateResearch.getResist()
    plateResearch.getPlateSize()
    plateResearch.getMovement()

    rawAskPriceList, rawBidPriceList, rawAskVolumeList, rawBidVolumeList, rawVolumeList, rawAmountList, \
    rawLastPriceList, rawTimeStampList = pc.processStockData(stockData, 93000, 150000)


    spaceResearch = space.spaceResearch(rawAskPriceList, rawBidPriceList, rawAskVolumeList, rawBidVolumeList,
                                        rawVolumeList, rawAmountList,rawLastPriceList, rawTimeStampList, platePercentSpace)

    spaceResearch.getRationalWidth()

    spaceResearch.getSpaceDuration()
    spaceResearch.getProcssData(timeStampList)

    slipList = slip.getSlipList(askPriceList, bidPriceList)
    sizeRatioList = plateResearch.sizeRatioList
    movementList = plateResearch.movementList
    spaceList = spaceResearch.processSpaceList
    durationList = spaceResearch.processDurationList
    highLowPointList = spaceResearch.processHighLowPointList



    #到这里为止，和上一次的代码是一样的，我们直接把需要的因子全都算出来

    #直接计算信号
    longOpenSignalList = signal.getLongSignalList(sizeRatioList,movementList,highLowPointList,durationList,spaceList,slipList,
                      minSizeRatioOpen,minMovementOpen,maxDurationOpen,maxSlipOpen)
    shortCloseSignalList = signal.getShortSignalList(sizeRatioList,movementList,spaceList,slipList,maxSizeRatioClose,maxMovementClose,maxSlipClose)

    #得到当天的回测结果
    profitList =sbt.simpleBackTest(askPriceList, bidPriceList, longOpenSignalList, shortCloseSignalList, maxPosition)

    return profitList

#获得一段时间的结果
def getTotalTest(startDate, endDate, stockCode,startTime, endTime,platePercentPlate,platePercentSpace,minSizeRatioOpen,minMovementOpen,
                   maxDurationOpen,maxSlipOpen,maxSlipClose,maxSizeRatioClose,maxMovementClose,maxPosition):

    file_list=filedata.path_name(startDate,endDate,stockCode)

    totalProfitList = []

    for path_csv in file_list:
        try:
            profitList = getDailyResult(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,minSizeRatioOpen,minMovementOpen,
                   maxDurationOpen,maxSlipOpen,maxSlipClose,maxSizeRatioClose,maxMovementClose,maxPosition)
        except:
            continue
        else:
            for n in profitList:
                totalProfitList.append(n)

    if len(totalProfitList) ==0:
        meanLong = 0
    else:
        meanLong = np.mean(totalProfitList)

    return meanLong,len(totalProfitList)

if __name__ == '__main__':


    stockCode = "300274.SZ"

    startTime = 93500
    endTime = 145000

    startDate = 20190821
    endDate = 20190821

    platePercentPlate = 0.002
    platePercentSpace = 0.001

    #开仓方向
    #盘口压单量因子，最低阈值
    minSizeRatioOpen = 2.0
    #盘口动量因子，最低阈值
    minMovementOpen = 2.0
    #波动率因子，最低阈值
    maxDurationOpen = 9.0
    #滑点因子，最大可承受阈值
    maxSlipOpen = 5.0

    #平仓方向
    #滑点因子，最大可承受阈值
    maxSlipClose = 5.0
    #盘口压单量因子，最大可承受阈值
    maxSizeRatioClose = 0.5
    #盘口动量因子，最大可承受阈值
    maxMovementClose = -1.0

    maxPosition = 1

    print(getTotalTest(startDate, endDate, stockCode, startTime, endTime, platePercentPlate, platePercentSpace,
                 minSizeRatioOpen, minMovementOpen,maxDurationOpen, maxSlipOpen,maxSlipClose, maxSizeRatioClose, maxMovementClose, maxPosition))

# (55.50416281220973, 1)
