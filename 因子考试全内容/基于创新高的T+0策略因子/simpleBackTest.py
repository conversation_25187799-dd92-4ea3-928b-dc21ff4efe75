point=0.000001
# -*- coding: utf-8 -*-

#简单的单日回测
def simpleBackTest(askPriceList,bidPriceList,longSignalList,shortSignalList,maxPosition):

    totalPosition = 0
    position = 0
    profitList = []

    for i in range(0,len(askPriceList)):

        #当前没有持仓，且还有底仓
        if position ==0 and totalPosition < maxPosition - point:

            #出了多头信号
            if longSignalList[i] == 1:

                #干进去
                position = 1
                #占用底仓+1
                totalPosition +=1
                #记录下开仓价格，使用对手价
                openPrice = askPriceList[i][0]
                continue

        #当前持有多仓
        if position ==1:

            #出了空头信号
            if shortSignalList[i] == 1:

                #平掉
                position = 0
                #记录下这一笔开平的收益，平仓价为对手价
                profitList.append((bidPriceList[i][0] - openPrice)/openPrice*10000.0)

    #到时间了，还拿着多头
    if position ==1:
        #没跌停
        if bidPriceList[-1][0]> point:
            #最后那个tick的对手价平掉
            profitList.append((bidPriceList[-1][0] - openPrice) / openPrice * 10000.0)
        #跌停了
        else:
            #跌停价平掉
            profitList.append((askPriceList[-1][0] - openPrice) / openPrice * 10000.0)

    #返回每次开平的收益
    return profitList



