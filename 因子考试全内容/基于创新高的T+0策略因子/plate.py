point = 0.00001
import numpy as np

def getEffectivePrice(priceList):
    '''不需要重写'''
    i = 1
    while i<= len(priceList):
        if priceList[-i] > point:
            return priceList[-i]
        i+=1
    return priceList[0]

def getAskPriceLimit(askPrice,lastAskPrice,width):
    '''不需要重写'''
    maxAsk = getEffectivePrice(askPrice)
    maxLastAsk = getEffectivePrice(lastAskPrice)

    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)

def getBidPriceLimit(bidPrice,lastBidPrice,width):
    '''不需要重写'''
    minBid = getEffectivePrice(bidPrice)
    minLastBid =getEffectivePrice(lastBidPrice)

    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price,askPrice,askVolume):
    '''不需要重写'''
    volumeBelow = 0
    for i in range(0,len(askPrice)):

        if askPrice[i] < price + point and askPrice[i] > point:

            volumeBelow += askVolume[i]

    return volumeBelow

def volumeUpPrice(price,bidPrice,bidVolume):
    '''不需要重写'''
    volumeUp = 0
    for i in range(0, len(bidPrice)):

        if bidPrice[i] > price - point and bidPrice[i] > point:
            volumeUp += bidVolume[i]

    return volumeUp

def threeSecondMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):
    '''不需要重写'''
    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)
    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)

    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)
    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)

    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)
    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)

    deltaAsk = lastVolumeBelow - volumeBelow
    deltaBid = volumeUp - lastVolumeUp

    return deltaAsk + deltaBid

def longResist(askPrice,askVolume,width):
    '''不需要重写'''
    resist = 0

    for i in range(0,10):

        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:

            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):
    '''不需要重写'''
    resist = 0
    for i in range(0, 10):

        if bidPrice[i] > bidPrice[0] - width - point:
            resist += bidVolume[i]

    return resist

class plateResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.movementList = [0]
        self.sizeRatioList = []

    def getRationalWidth(self):
        '''不需要重写'''
        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01

    def getResist(self):
        '''不需要重写'''
        for i in range(0, len(self.askPriceList)):
            self.longResistList.append(longResist(self.askPriceList[i], self.askVolumeList[i], self.width))
            self.shortResistList.append(shortResist(self.bidPriceList[i], self.bidVolumeList[i], self.width))

    def getMovement(self):
        '''不需要重写'''
        for i in range(1, len(self.askPriceList)):

            movement = threeSecondMovement(self.askPriceList[i - 1], self.bidPriceList[i - 1], self.askVolumeList[i - 1], self.bidVolumeList[i - 1],
                                self.askPriceList[i], self.bidPriceList[i], self.askVolumeList[i], self.bidVolumeList[i],self.width)
            if movement > point:
                self.movementList.append(movement/self.longResistList[i])
            elif movement < - point:
                self.movementList.append(movement / self.shortResistList[i])
            else:
                self.movementList.append(0)

    def getPlateSize(self):

        for i in range(0, len(self.shortResistList)):


            self.sizeRatioList.append(self.shortResistList[i]/self.longResistList[i])



