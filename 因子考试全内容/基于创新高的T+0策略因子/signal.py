# -*- coding: utf-8 -*-
point=0.0000001

def getLongSignalList(sizeRatioList,movementList,highLowPointList,durationList,spaceList,slipList,
                      minSizeRatio,minMovement,maxDuration,maxSlip):

    longSignalList = []

    for i in range(0,len(sizeRatioList)):

        #简单多开信号
        if sizeRatioList[i]> minSizeRatio- point and movementList[i] > minMovement-point and durationList[i]<maxDuration+point\
                and spaceList[i] >=2 and highLowPointList[i]==1 and slipList[i] < maxSlip + point:
            longSignalList.append(1)
        else:
            longSignalList.append(0)

    return longSignalList

def getShortSignalList(sizeRatioList,movementList,spaceList,slipList,maxSizeRatio,maxMovement,maxSlip):

    shortSignalList = []

    for i in range(0, len(sizeRatioList)):

        #简单空平信号
        if sizeRatioList[i] < maxSizeRatio + point and movementList[i] < maxMovement + point and spaceList[i] <= -1 and\
                slipList[i] < maxSlip + point:
            shortSignalList.append(1)
        else:
            shortSignalList.append(0)

    return shortSignalList




