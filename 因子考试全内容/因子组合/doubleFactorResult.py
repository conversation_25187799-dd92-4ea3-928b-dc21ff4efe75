# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import plate
import space
import factorScoring as fs
import signalEstimateTwap as set
import filedata

import numpy as np
import pandas
import multiprocessing
from tqdm import tqdm
processNumber = 48

def getDailyResult(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,factorBoundaryList,targetScore,targetHighLowPoint,
                   tWapDuration,signalDuration):


    stockData = pd.read_csv(path_csv).values


    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)


    plateResearch = plate.plateResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercentPlate)

    plateResearch.getRationalWidth()
    plateResearch.getResist()
    plateResearch.getPlateSize()

    #盘口因子
    sizeRatioFactorList = plateResearch.sizeRatioList#盘口买卖量的比例
    sizeRatioScoreList = fs.getScoreList(sizeRatioFactorList,factorBoundaryList)#买卖量的比例变为0，1，2，3的区间标记
    sizeRatioSignalList = fs.getSingleFactorSignalList(sizeRatioScoreList,targetScore)#根据指定的区间标记，转换为开仓的信号1，0


    rawAskPriceList, rawBidPriceList, rawAskVolumeList, rawBidVolumeList, rawVolumeList, rawAmountList, \
    rawLastPriceList, rawTimeStampList = pc.processStockData(stockData, 93000, 150000)


    spaceResearch = space.spaceResearch(rawAskPriceList, rawBidPriceList, rawAskVolumeList, rawBidVolumeList,
                                        rawVolumeList, rawAmountList, rawLastPriceList, rawTimeStampList,platePercentSpace)

    spaceResearch.getRationalWidth()

    spaceResearch.getSpaceDuration()
    spaceResearch.getProcssData(timeStampList)


    #高低点因子
    highLowPointList = spaceResearch.processHighLowPointList#指定时间区间内的高低点
    highLowPointSignalList = fs.getSingleFactorSignalList(highLowPointList,targetHighLowPoint)#通过-1，0，1等高低点


    #混合起来
    signalList = fs.combineSignal(sizeRatioSignalList,highLowPointSignalList)

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    profitListLong = signalEstimate.longSignalEstimateTwap(signalDuration, signalList)

    return profitListLong

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercentPlate,platePercentSpace,factorBoundaryList,targetScore,tWapDuration,signalDuration,targetHighLowPoint):

    file_list=filedata.path_name(startDate,endDate,stockCode)


    totalProfitListLong = []

    for path_csv in file_list:
        # try:
            profitListLong = getDailyResult(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,factorBoundaryList,targetScore,targetHighLowPoint,
                   tWapDuration,signalDuration)


        # except:
        #     continue
        # else:
            for n in profitListLong:
                totalProfitListLong.append(n)

    if len(totalProfitListLong) ==0:
        meanLong = 0
    else:
        meanLong = np.mean(totalProfitListLong)

    return targetScore,targetHighLowPoint,meanLong,len(totalProfitListLong)

def getDoubleFactorResult(startDate,endDate,stockCode,startTime, endTime,platePercentPlate,platePercentSpace,tWapDuration,signalDuration,factorBoundaryList):



    #多线程
    ret_list = []
    pbar = tqdm(total=3 * len(factorBoundaryList),
                desc='{} calc para'.format(pandas.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')))
    pool = multiprocessing.Pool(min(multiprocessing.cpu_count(), processNumber))
    for targetHighLowPoint in [-1,0,1]:
        for targetScore in range(0, len(factorBoundaryList)+1):
            ret_list.append(pool.apply_async(getTotalTest, args=(
                startDate,endDate,stockCode,startTime, endTime,platePercentPlate,platePercentSpace,factorBoundaryList,
                targetScore,tWapDuration,signalDuration,targetHighLowPoint),
                                             callback=lambda x: pbar.update()))

    pool.close()
    pool.join()
    pbar.close()

    ret_list = list(map(lambda x: x.get(), ret_list))
    sizeRatioScoreList = [g[0] for g in ret_list]
    highLowPointScoreList = [g[1] for g in ret_list]
    meanProfitLongList = [g[2] for g in ret_list]
    lengthProfitLongList = [g[3] for g in ret_list]

    df = pd.DataFrame({
        'highLowPointScore': highLowPointScoreList,
        'sizeRatioScore': sizeRatioScoreList,
        'meanProfitLong': meanProfitLongList,
        'lengthProfitLong': lengthProfitLongList, }, index=None)
    print(df)
    #
    # ret_df1 = df[['highLowPointScore', 'sizeRatioScore', 'meanProfitLong']].set_index(
    #     ['highLowPointScore', 'sizeRatioScore']).unstack()
    #
    #
    # ret_df2 = df[['highLowPointScore', 'sizeRatioScore', 'lengthProfitLong']].set_index(
    #     ['highLowPointScore', 'sizeRatioScore']).unstack()
    #
    # ret_df = pd.concat([ret_df1,ret_df2],axis=1)
    #
    #
    # str = 'sizeRatioPlusHighLowPoint.csv'
    # ret_df.to_csv(str, encoding='gbk', )

if __name__ == '__main__':

    stockCode = "300274.SZ"
    startTime = 93100
    endTime = 145000
    platePercentPlate = 0.002
    platePercentSpace = 0.002

    startDate = 20190610
    endDate = 20190610

    # factorBoundaryList =[0.011,0.018,0.031,0.053,0.083,0.14,0.22,0.4,0.7,1.4,2.5,4.5,7.2,12.0,19.0,32.0,55.0,90.0]
    factorBoundaryList = [ 0.9, 2.5, 5]
    tWapDuration = 60
    signalDuration=60

    getDoubleFactorResult(startDate, endDate, stockCode, startTime, endTime, platePercentPlate, platePercentSpace,
                          tWapDuration, signalDuration, factorBoundaryList)







#     highLowPointScore  sizeRatioScore  meanProfitLong  lengthProfitLong
# 0                  -1               0        7.503859                 3
# 1                  -1               1       17.696850                 3
# 2                  -1               2       38.537351                 2
# 3                  -1               3       25.412628                 1
# 4                   0               0       -0.854592               104
# 5                   0               1        2.473121               142
# 6                   0               2        4.697866                68
# 7                   0               3       10.427544                14
# 8                   1               0        0.050714                30
# 9                   1               1        2.938470                22
# 10                  1               2        0.000000                 0
# 11                  1               3        0.000000                 0