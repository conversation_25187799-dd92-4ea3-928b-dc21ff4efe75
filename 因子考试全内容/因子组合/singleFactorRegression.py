# -*- coding: utf-8 -*-
import processData as pc
import pandas as pd
import plate
import factorScoring as fs
import signalEstimateTwap as set
import numpy as np
from sklearn import linear_model
import matplotlib.pyplot as plt
import filedata


def getDailyResult(path_csv,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration):

    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)

    plateResearch = plate.plateResearch(askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList,
                                        amountList, lastPriceList, timeStampList, platePercent)
    plateResearch.getRationalWidth()
    plateResearch.getResist()
    plateResearch.getPlateSize()

    factorList = plateResearch.sizeRatioList#买卖量比列表[0.41219350563286944, 2.128939828080229, 1.0364511691884457, 1.829560585885486,...]
    scoreList = fs.getScoreList(factorList,factorBoundaryList)#[0.5,1]对应3个区间
    signalList = fs.getSingleFactorSignalList(scoreList,targetScore)#targetScore对应第几个区间

    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, lastPriceList, tWapDuration, timeStampList)
    profitListLong = signalEstimate.longSignalEstimateTwap(signalDuration, signalList)#多头twap
    return profitListLong

def getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration):

    file_list=filedata.path_name(startDate,endDate,stockCode)
    totalProfitListLong = []

    for path_csv in file_list:
        # try:
            profitListLong = getDailyResult(path_csv,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration)
        # except:
        #     continue
        # else:
            for n in profitListLong:
                totalProfitListLong.append(n)

    if len(totalProfitListLong) ==0:
        meanLong = 0
    else:
        meanLong = np.mean(totalProfitListLong)
    return meanLong,len(totalProfitListLong)

def getRegressionResult(startDate,endDate,stockCode,startTime, endTime,platePercent,tWapDuration,signalDuration,factorBoundaryList):

    alpha = []
    score = []
    for targetScore in range(0,len(factorBoundaryList)+1):

        profitLong,lengthLong = getTotalTest(startDate,endDate,stockCode,startTime, endTime,platePercent,factorBoundaryList,targetScore,tWapDuration,signalDuration)
        print(targetScore,profitLong,lengthLong)
        if lengthLong >= 50:
            alpha.append(profitLong)
            score.append([targetScore])

    #线性回归模型
    model = linear_model.LinearRegression()
    model.fit(score, alpha)
    print(model.coef_)
    print(model.intercept_)
    print(model.score(score, alpha))

    #线性模型的预测值
    expectAlpha = []
    for i in range(0,len(score)):
        expectAlpha.append([score[i][0]*model.coef_[0]+model.intercept_])

    #画图
    plt.plot(score,alpha,'ro')
    plt.plot(score,expectAlpha)
    plt.show()

if __name__ == '__main__':

    stockCode = "300274.SZ"
    startTime = 93100
    endTime = 145000
    platePercent = 0.002

    startDate = 20190610
    endDate = 20190610

    # factorBoundaryList =[0.011,0.018,0.031,0.053,0.083,0.14,0.22,0.4,0.7,1.4,2.5,4.5,7.2,12.0,19.0,32.0,55.0,90.0]
    factorBoundaryList = [ 0.9, 2.5, 5]
    tWapDuration = 60
    signalDuration=60

    getRegressionResult(startDate, endDate, stockCode, startTime, endTime, platePercent, tWapDuration, signalDuration,
                        factorBoundaryList)



# 0 -0.2755792133320578 130
# 1 2.353900362455395 160
# 2 5.515992712619861 69
# 3 11.426549929095072 15
# [2.89578596]
# -0.3643480090615592
# 0.9971888345867781