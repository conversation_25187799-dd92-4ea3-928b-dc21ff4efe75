point = 0.000001
import numpy as np
import timeGap as tg

def longSignalEstimate(askPrceList,bidPriceList,index,lastPriceList,duration,timeStampList):

    tradePrice = 0.5*(askPrceList[index][0]+bidPriceList[index][0])
    if lastPriceList[index] ==0:
        tWapList = [tradePrice]
    else:
        tWapList = [lastPriceList[index]]

    j = index +1
    while tg.timeGap(timeStampList[index],timeStampList[j]) < duration+point and j <= len(lastPriceList)-2:
        if lastPriceList[j] !=0:
            tWapList.append(lastPriceList[j])
        else:
            tWapList.append(tradePrice)
        j+=1

    tWap = np.mean(tWapList)
    profit = tWap - tradePrice

    return profit/tWap * 10000.0

def shortSignalEstimate(askPrceList,bidPriceList,index,lastPriceList,duration,timeStampList):

    tradePrice =  0.5*(askPrceList[index][0]+bidPriceList[index][0])
    if lastPriceList[index] == 0:
        tWapList = [tradePrice]
    else:
        tWapList = [lastPriceList[index]]

    j = index + 1
    while tg.timeGap(timeStampList[index], timeStampList[j]) < duration + point and j <= len(lastPriceList) - 2:
        if lastPriceList[j] != 0:
            tWapList.append(lastPriceList[j])
        else:
            tWapList.append(tradePrice)
        j += 1

    tWap = np.mean(tWapList)
    profit = tradePrice-tWap

    return profit / tWap * 10000.0

class signalEstimate:

    def __init__(self,askPriceList,bidPriceList,lastPriceList,tWapDuration,timeStampList):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.lastPriceList = lastPriceList
        self.tWapDuration = tWapDuration
        self.timeStampList = timeStampList

    def longSignalEstimateTwap(self,signalDuration,longSignalList):

        profitList = []
        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            if position == 0:

                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.tWapDuration:
                    break

                if longSignalList[i] > point:
                    startTime = self.timeStampList[i]
                    position = 1
                    profitList.append(longSignalEstimate(self.askPriceList, self.bidPriceList,i, self.lastPriceList, self.tWapDuration, self.timeStampList))
            else:
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    position = 0

        return profitList

    def shortSignalEstimateTwap(self,signalDuration,shortSignalList):

        profitList = []
        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            if position == 0:

                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.tWapDuration:
                    break

                if shortSignalList[i] > point:
                    startTime = self.timeStampList[i]
                    position = 1
                    profitList.append(shortSignalEstimate(self.askPriceList, self.bidPriceList, i, self.lastPriceList, self.tWapDuration, self.timeStampList))
            else:
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    position = 0

        return profitList