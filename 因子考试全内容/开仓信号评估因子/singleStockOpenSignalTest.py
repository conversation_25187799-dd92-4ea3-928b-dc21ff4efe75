# -*- coding: utf-8 -*-
import processData as pc
import plate
import space
import slip
import signalEstimateT0 as set
import numpy as np
import pandas as pd
import filedata

def combineSignal(signalList1,signalList2,signalList3):

    signalList = []
    for i in range(0,len(signalList1)):

        if signalList1[i]==1 and signalList2[i]==1 and signalList3[i]==1:
            signalList.append(1)
        else:
            signalList.append(0)

    return signalList

#评价一天的信号结果
def getDailyTest(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,ratioLowPlateSize,ratioHighPlateSize,
                 ratioLowMovement, ratioHighMovement,spaceLow,spaceHigh,durationLow, durationHigh,highLowPointLow, highLowPointHigh,
                 slipLow,slipHigh,maxDrawDownPercent,maxDuration,signalDuration):

    #数据预处理，获取二维数组
    stockData = pd.read_csv(path_csv).values
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pc.processStockData(
        stockData, startTime, endTime)
    #生成盘口研究的类
    plateResearch = plate.plateResearch(askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercentPlate)
    #将比率转化为价格
    plateResearch.getRationalWidth()#宽度
    #计算每个盘口的多空阻力
    plateResearch.getResist()#在有效价格范围内的盘口买卖量分别组成的列表

    #计算每个盘口的盘口压单比因子信号结果
    plateResearch.getPlateSizeSignal(ratioLowPlateSize,ratioHighPlateSize)
    #计算每个盘口的动量
    plateResearch.getMovement()
    #计算每个盘口的动量因子信号结果
    plateResearch.getMovementSignal(ratioLowMovement, ratioHighMovement)
    #混合动量因子与盘口压单量因子
    plateResearch.getCombineSignal()

    #取一份原始数据，从9点30分开始，到下午3点结束
    rawAskPriceList, rawBidPriceList, rawAskVolumeList, rawBidVolumeList, rawVolumeList, rawAmountList, \
    rawLastPriceList, rawTimeStampList = pc.processStockData(stockData, 93000, 150000)

    #生成空间研究的类
    spaceResearch = space.spaceResearch(rawAskPriceList,rawBidPriceList,rawAskVolumeList,rawBidVolumeList,rawVolumeList,rawAmountList,
                                      rawLastPriceList,rawTimeStampList,platePercentSpace)

    #将比率转化为价格
    spaceResearch.getRationalWidth()
    #计算空间K线
    spaceResearch.getSpaceDuration()
    #计算空间因子信号
    spaceResearch.getSpaceSignal(spaceLow,spaceHigh)
    #计算波动率因子信号
    spaceResearch.getDurationSignal(durationLow, durationHigh)
    #计算高低点因子信号
    spaceResearch.getHighLowPointSignal(highLowPointLow, highLowPointHigh)
    #混合三种信号
    spaceResearch.combineSignal()
    #以需要研究的时间段，切出需要的信号，从而可以跟盘口类因子进行混合
    spaceResearch.getSignal(timeStampList)

    #计算每个盘口的滑点成本
    slipList = slip.getSlipList(askPriceList, bidPriceList)


    #生成5类信号，分别为，滑点信号，多头盘口类信号，空头盘口类信号，多头空间类信号，空头空间类信号
    slipSignalList = slip.getSlipSignalList(slipList,slipLow,slipHigh)
    longPlateSignalList = plateResearch.longPlateSignalList
    shortPlateSignalList = plateResearch.shortPlateSignalList
    longSpaceSignalList = spaceResearch.longSignalList
    shortSpaceSignalList = spaceResearch.shortSignalList


    #合并滑点信号，多头盘口类信号，多头空间类信号，形成最终的多头信号
    longSignalList = combineSignal(slipSignalList,longPlateSignalList,longSpaceSignalList)
    # 合并滑点信号，空头盘口类信号，空头空间类信号，形成最终的空头信号
    shortSignalList = combineSignal(slipSignalList,shortPlateSignalList,shortSpaceSignalList)

    #生成信号评价的类
    signalEstimate = set.signalEstimate(askPriceList, bidPriceList, maxDrawDownPercent,maxDuration,timeStampList)
    #计算最大回撤，将比率转化为价格
    signalEstimate.getMaxDrawDown()
    #评价当天的多头信号
    maxLongProfitListOpp, maxLongProfitListMate, longProfitListStop = signalEstimate.longSignalEstimateT0(signalDuration,longSignalList)
    #评价当天的空头信号
    maxShortProfitListOpp, maxShortProfitListMate, shortProfitListStop = signalEstimate.shortSignalEstimateT0(signalDuration,shortSignalList)

    #返回当天的结果
    return maxLongProfitListOpp, maxLongProfitListMate, longProfitListStop,maxShortProfitListOpp, maxShortProfitListMate, shortProfitListStop

#评价一段日期的结果
def getTotalTest(stockCode, startTime, endTime, platePercentPlate, platePercentSpace, ratioLowPlateSize,
                 ratioHighPlateSize,ratioLowMovement, ratioHighMovement, spaceLow, spaceHigh, durationLow, durationHigh, highLowPointLow,
                 highLowPointHigh,slipLow, slipHigh, maxDrawDownPercent, maxDuration, signalDuration, startDate, endDate):

    file_list=filedata.path_name(startDate,endDate,stockCode)
    print(file_list)
    #获取要读取的所有文件路径
    totalMaxLongProfitListOpp = []
    totalMaxLongProfitListMate = []
    totalLongProfitListStop = []
    totalMaxShortProfitListOpp = []
    totalMaxShortProfitListMate = []
    totalShortProfitListStop = []

    for path_csv in file_list:
        # try:

        maxLongProfitListOpp, maxLongProfitListMate, longProfitListStop,maxShortProfitListOpp, maxShortProfitListMate, shortProfitListStop= \
             getDailyTest(path_csv,startTime, endTime,platePercentPlate,platePercentSpace,ratioLowPlateSize,ratioHighPlateSize,
             ratioLowMovement, ratioHighMovement,spaceLow,spaceHigh,durationLow, durationHigh,highLowPointLow, highLowPointHigh,
             slipLow,slipHigh,maxDrawDownPercent,maxDuration,signalDuration)

        # except:
        #     continue
        # else:
        #
        for n in maxLongProfitListOpp:
            totalMaxLongProfitListOpp.append(n)
        for n in maxLongProfitListMate:
            totalMaxLongProfitListMate.append(n)
        for n in longProfitListStop:
            totalLongProfitListStop.append(n)
        for n in maxShortProfitListOpp:
            totalMaxShortProfitListOpp.append(n)
        for n in maxShortProfitListMate:
            totalMaxShortProfitListMate.append(n)
        for n in shortProfitListStop:
            totalShortProfitListStop.append(n)

    if len(totalMaxLongProfitListOpp) == 0:
        meanMaxLongProfitListOpp = -10000
        meanMaxLongProfitListMate = -10000
        meanLongProfitListStop = -10000

    else:
        meanMaxLongProfitListOpp = np.mean(totalMaxLongProfitListOpp)
        meanMaxLongProfitListMate =np.mean(totalMaxLongProfitListMate)
        meanLongProfitListStop = np.mean(totalLongProfitListStop)

    if len(totalMaxShortProfitListOpp) == 0:
        meanMaxShortProfitListOpp = -10000
        meanMaxShortProfitListMate = -10000
        meanShortProfitListStop = -10000

    else:
        meanMaxShortProfitListOpp = np.mean(totalMaxShortProfitListOpp)
        meanMaxShortProfitListMate = np.mean(totalMaxShortProfitListMate)
        meanShortProfitListStop = np.mean(totalShortProfitListStop)

    return meanMaxLongProfitListOpp,meanMaxLongProfitListMate,meanLongProfitListStop,len(totalMaxLongProfitListOpp),\
           meanMaxShortProfitListOpp,meanMaxShortProfitListMate,meanShortProfitListStop,len(totalMaxShortProfitListOpp)


if __name__ == '__main__':

    #要回测的股票
    stockCode = "300274.SZ"

    #关心的交易时间段
    startTime = 93500
    endTime = 145000

    #关心的交易日，开始日期，结束日期
    startDate = 20190610
    endDate = 20190620

    #信号与信号之间的最短采样间隔
    signalDuration = 60

    #盘口因子使用的宽度，比如，千二
    platePercentPlate = 0.002
    #空间因子使用的宽度，比如，千一
    platePercentSpace = 0.001

    #能接受的滑点的范围，对手价与盘口中间价的差，单位是bp
    slipLow = 0
    slipHigh=520

    #是否需要创新高，新低才进场，只允许取1为，必须新高，新低才进场
    #0或者1皆可，表示无所谓，只能取0，表示，新高，新低的时候不进程，作为对比使用
    highLowPointLow = 0
    highLowPointHigh = 1

    #盘口类，盘口动量因子的范围
    ratioLowMovement =2.0
    ratioHighMovement = 100000.0

    #空间类，空间因子的范围
    spaceLow =5.0
    spaceHigh = 10000

    #空间类，波动率因子的范围
    durationLow = 0
    durationHigh= 60

    #盘口类，盘口压单量因子的范围
    ratioLowPlateSize = 2.0
    ratioHighPlateSize = 10000.2

    #信号评价类，回撤超过某个比例，比如，千一，或者，一段时间没创新高，代表信号结束
    maxDrawDownPercent = 0.001
    maxDuration = 60

    #输出最终结果
    print(getTotalTest(stockCode, startTime, endTime, platePercentPlate, platePercentSpace, ratioLowPlateSize,
                 ratioHighPlateSize,ratioLowMovement, ratioHighMovement, spaceLow, spaceHigh, durationLow, durationHigh, highLowPointLow,
                 highLowPointHigh,slipLow, slipHigh, maxDrawDownPercent, maxDuration, signalDuration, startDate, endDate))

    #(31.23100708973819, 17.339407630104336, 0.9414095805602558, 9, 44.79099386184209, 28.29520427290943, 6.821409608182375, 11)

