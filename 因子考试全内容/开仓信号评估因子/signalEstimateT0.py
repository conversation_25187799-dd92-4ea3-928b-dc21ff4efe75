# -*- coding: utf-8 -*-
point = 0.000001
import timeGap as tg
import numpy as np

#多头信号评价
def longSignalEstimate(askPriceList,bidPriceList,index,maxDrawDown,duration,timeStampList):

    #假设成交价是卖一价
    tradePrice = askPriceList[index][0]
    #记录卖一价的最大值
    maxAskPrice = askPriceList[index][0]
    #记录买一价的最大值
    maxBidPrice = bidPriceList[index][0]
    #记录创新高的时间点
    startTime = timeStampList[index]

    for i in range(index+1,len(askPriceList)):

        #如果卖一价创新高
        if askPriceList[i][0] > maxAskPrice + point:
            #更新卖一价新高
            maxAskPrice = askPriceList[i][0]
        #如果买一价创新高
        if bidPriceList[i][0] > maxBidPrice + point:
            #更新买一价新高
            maxBidPrice = bidPriceList[i][0]
            #更新最后创新高的时间
            startTime = timeStampList[i]

        #如果超过了duration时间没有创新高，或者，卖一价已经回撤了超过maxDrawDown
        if tg.timeGap(startTime,timeStampList[i]) > duration + point or maxAskPrice - askPriceList[i][0] > maxDrawDown + point:
            #返回，最高卖一减去成交价，最高买一减去成交价，信号结束时间点买一减去成交价
            return (maxAskPrice - tradePrice)/tradePrice*10000.0,(maxBidPrice - tradePrice)/tradePrice*10000.0,(bidPriceList[i][0] - tradePrice)/tradePrice*10000.0

    return (maxAskPrice - tradePrice)/tradePrice*10000.0,(maxBidPrice - tradePrice)/tradePrice*10000.0,(bidPriceList[-1][0] - tradePrice)/tradePrice*10000.0

#空头信号评价
def shortSignalEstimate(askPriceList,bidPriceList,index,maxDrawDown,duration,timeStampList):

    tradePrice = bidPriceList[index][0]
    minAskPrice = askPriceList[index][0]
    minBidPrice = bidPriceList[index][0]
    startTime = timeStampList[index]

    for i in range(index + 1, len(askPriceList)):
        if askPriceList[i][0] < minAskPrice - point:
            minAskPrice = askPriceList[i][0]
            startTime = timeStampList[i]
        if bidPriceList[i][0] < minBidPrice - point:
            minBidPrice = bidPriceList[i][0]

        if tg.timeGap(startTime,timeStampList[i]) > duration + point or bidPriceList[i][0] - minBidPrice > maxDrawDown + point:
            return (tradePrice - minBidPrice)/tradePrice *10000.0,(tradePrice - minAskPrice)/tradePrice *10000.0,(tradePrice - askPriceList[i][0])/tradePrice *10000.0
    return (tradePrice - minBidPrice)/tradePrice *10000.0,(tradePrice - minAskPrice)/tradePrice *10000.0,(tradePrice - askPriceList[-1][0])/tradePrice *10000.0

class signalEstimate:

    def __init__(self,askPriceList,bidPriceList,maxDrawDownPercent,maxDuration,timeStampList):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.maxDrawDownPercent = maxDrawDownPercent
        self.maxDuration = maxDuration
        self.timeStampList = timeStampList
        self.maxDrawDown = 0

    #将最大回撤的比例转化为实际价格，比如，100块的票，回撤千一，是1毛钱
    def getMaxDrawDown(self):
        '''不需要重写'''
        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.maxDrawDown = np.ceil(price * self.maxDrawDownPercent / 0.01) * 0.01

    #当天一系列多头信号的综合评价
    def longSignalEstimateT0(self,signalDuration,longSignalList):

        #按对手价记录的最大利润
        maxProfitListOpp = []
        #按本方价记录的最大利润，理论上可实现，只要你平得足够好
        maxProfitListMate = []
        #如果每次都止损出场的利润
        profitListStop = []

        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            #如果没有模拟持仓
            if position == 0:

                #离收盘时间已经不足maxDuration的信号，不评价了
                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.maxDuration:
                    break

                #多头信号出现
                if longSignalList[i] > point:
                    #记录多头信号时间点
                    startTime = self.timeStampList[i]
                    #模拟持仓为1
                    position = 1
                    #评价这个信号
                    maxProfitOpp,maxProfitMate,profitStop =longSignalEstimate(self.askPriceList, self.bidPriceList,i, self.maxDrawDown, self.maxDuration, self.timeStampList)
                    #记录下评价结果
                    maxProfitListOpp.append(maxProfitOpp)
                    maxProfitListMate.append(maxProfitMate)
                    profitListStop.append(profitStop)
            #如果有模拟持仓(之前出现了一个信号)
            else:
                #如果距离上一个信号，过了signalDuration时间
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    #模拟持仓恢复为0，这样，就可以去评价下一个信号了
                    position = 0
            #这一段目的是为了避免连续出现信号，保证两个信号之间至少间隔signalDuration

        return maxProfitListOpp,maxProfitListMate,profitListStop

    def shortSignalEstimateT0(self,signalDuration,shortSignalList):

        maxProfitListOpp = []
        maxProfitListMate = []
        profitListStop = []

        position = 0
        startTime = 0

        for i in range(0, len(self.timeStampList)):

            if position == 0:

                if tg.timeGap(self.timeStampList[i], self.timeStampList[-1]) < self.maxDuration:
                    break

                if shortSignalList[i] > point:
                    startTime = self.timeStampList[i]
                    position = 1
                    maxProfitOpp,maxProfitMate,profitStop = shortSignalEstimate(self.askPriceList, self.bidPriceList, i, self.maxDrawDown, self.maxDuration, self.timeStampList)
                    maxProfitListOpp.append(maxProfitOpp)
                    maxProfitListMate.append(maxProfitMate)
                    profitListStop.append(profitStop)
            else:
                if tg.timeGap(startTime, self.timeStampList[i]) > signalDuration - point:
                    position = 0

        return maxProfitListOpp,maxProfitListMate,profitListStop