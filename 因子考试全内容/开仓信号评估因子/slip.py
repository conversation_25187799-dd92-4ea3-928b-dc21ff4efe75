point = 0.0000001

#滑点计算，我们之前构建因子用的是盘口中间价
#写开仓信号，需要考虑实际成交价与盘口中间价的差别，即额外成本
#这里简单假设成本价等于对手价，将来这个部分可以强化
def getSlipList(askPriceList,bidPriceList):

    slipList = []

    for i in range(0,len(askPriceList)):

        slipList.append((askPriceList[i][0]-bidPriceList[i][0])/(askPriceList[i][0]+bidPriceList[i][0])*10000.0)

    return slipList

#化为滑点信号，即，如果滑点过大，我就不做了，作为过滤信号
def getSlipSignalList(slipList,slipLow,slipHigh):

    signalList = []
    for i in range(0,len(slipList)):

        if slipList[i] > slipLow - point and slipList[i] < slipHigh + point:

            signalList.append(1)

        else:

            signalList.append(0)

    return signalList
