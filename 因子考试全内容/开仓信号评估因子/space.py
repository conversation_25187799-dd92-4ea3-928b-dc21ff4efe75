# -*- coding: utf-8 -*-
import numpy as np
import timeGap as tg
point = 0.0000001

class spaceResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,
                 platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.spaceList = [0]
        self.durationList = [0]
        self.highLowPointList = [0]

        self.longSpaceSignalList = []
        self.shortSpaceSignalList = []

        self.durationSignalList = []

        self.longHighLowPointSignalList = []
        self.shortHighLowPointSignalList = []

        self.rawLongSignalList = []
        self.rawShortSignalList = []

        self.longSignalList = []
        self.shortSignalList = []

    #将比例转化为价格，比如，100块的股票，千一是一毛钱
    def getRationalWidth(self):
        '''不需要重写'''

        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01


    #核心函数，计算空间K线
    def getSpaceDuration(self):

        #当前空间K线经历了多少时间
        duration = 0
        #上一个空间K线花了多久才更新
        lastDuration = 0
        #空间因子
        space = 0
        #初始盘口中间价
        price = 0.5*(self.bidPriceList[0][0]+self.askPriceList[0][0])
        #所有空间K线的最高价
        maxPrice = price
        #所有空间K线的最低价
        minPrice = price
        #最高最低点标志
        highLowPoint = 0

        for i in range(1,len(self.askPriceList)):

            #买一突破上一个空间K线价格超过width
            if self.bidPriceList[i][0] - price > self.width - point:

                #如果之前不在多头趋势中
                if space < point:

                    #空间因子值为1
                    space = 1
                else:
                    #空间因子值加1
                    space += 1
                #新空间K线的价格
                price = self.bidPriceList[i][0]
                #记录下上一个空间K线花了多久才被打破
                lastDuration = duration
                #初始化当前空间K线的持续时间为0，即，新K线到来了
                duration = 0

                #新K线价格创新高
                if price > maxPrice + point:
                    #更新当天价格最高的空间K线价格
                    maxPrice = price
                    #当前位置处于高点
                    highLowPoint = 1

                else:
                    #当前位置既非高点，也非低点
                    highLowPoint = 0

            #卖一价跌破当前空间K线价格超过width
            elif price - self.askPriceList[i][0] > self.width - point:

                #当前不在空头趋势中
                if space > -point:
                    #空间因子为-1
                    space = -1
                else:
                    #空间因子再减1
                    space -= 1

                #新空间K线价格
                price = self.askPriceList[i][0]
                #更新前一个空间K线被打破花了多久
                lastDuration = duration
                #初始化当前空间K线经历了多久
                duration = 0

                #新K线价格创新低
                if price < minPrice - point:
                    #更新最低价
                    minPrice = price
                    #标记当前位置为低点
                    highLowPoint = -1

                else:
                    #否则当前位置既不是高点也不是低点
                    highLowPoint = 0
            #剩下的情况，就是空间K线不需要更新
            else:
                #空间K线的时间增加
                duration += tg.timeGap(self.timeStampList[i - 1], self.timeStampList[i])

            #记录当前tick的空间因子值
            self.spaceList.append(space)
            #以当前空间K线持续时间和上一个空间K线持续时间的较大者作为衡量波动率的因子
            self.durationList.append(max(duration,lastDuration))
            # print(space,lastDuration,duration,highLowPoint)
            # print(self.spaceList)
            # print(self.durationList)
            # a=input()
            #记录下高低点标记
            self.highLowPointList.append(highLowPoint)


    #空间因子转化为空间信号
    def getSpaceSignal(self,spaceLow,spaceHigh):

        for i in range(0, len(self.spaceList)):

            if self.spaceList[i] > spaceLow- point and self.spaceList[i]< spaceHigh + point:
                self.longSpaceSignalList.append(1)
            else:
                self.longSpaceSignalList.append(0)

            if self.spaceList[i] >-1.0* spaceHigh- point and self.spaceList[i]< -1.0*spaceLow + point:
                self.shortSpaceSignalList.append(1)
            else:
                self.shortSpaceSignalList.append(0)

    #波动率因子转化为波动率信号
    def getDurationSignal(self,durationLow,durationHigh):
        # print(durationLow,durationHigh,durationLow- point,durationHigh + point)
        # print(self.durationList)


        for i in range(0, len(self.durationList)):

            if self.durationList[i] > durationLow- point and self.durationList[i]< durationHigh + point:
                self.durationSignalList.append(1)
                # print(self.durationList[i], 1)
            else:
                self.durationSignalList.append(0)
                # print(self.durationList[i], 0)



    #高低点因子转化为高低点信号
    def getHighLowPointSignal(self,highLowPointLow,highLowPointHigh):


        for i in range(0, len(self.highLowPointList)):
            '''
            self.shortHighLowPointSignalList，突破高点的为多，标识为0，否则都为1
            '''


            if self.highLowPointList[i] > highLowPointLow -point and self.highLowPointList[i] < highLowPointHigh +point:
                self.longHighLowPointSignalList.append(1)
            else:
                self.longHighLowPointSignalList.append(0)

            if self.highLowPointList[i] > -1.0*highLowPointHigh -point and self.highLowPointList[i] < -1.0*highLowPointLow +point:
                self.shortHighLowPointSignalList.append(1)
            else:
                self.shortHighLowPointSignalList.append(0)

    #把三个信号取交集混合
    def combineSignal(self):

        for i in range(0,len(self.longSpaceSignalList)):

            if self.longSpaceSignalList[i] == 1 and self.durationSignalList[i] == 1 and self.longHighLowPointSignalList[i]==1:

                self.rawLongSignalList.append(1)
            else:
                self.rawLongSignalList.append(0)

            if self.shortSpaceSignalList[i] == 1 and self.durationSignalList[i] == 1 and self.shortHighLowPointSignalList[i]==1:
                self.rawShortSignalList.append(1)
            else:
                self.rawShortSignalList.append(0)

    #用于对时，目的在于和其他信号混合，因为空间K线，当天高低点，必须从9点30分开始做
    #其他信号，比如盘口类，可以从每天任何时候开始做
    #如果要关注特殊时间段，比如，10点以后，就得做一个对时，这个函数就起这个作用
    def getSignal(self,timeStampList):

        startTime = timeStampList[0]
        endTime = timeStampList[-1]

        for i in range(0,len(self.timeStampList)):

            if self.timeStampList[i] < startTime -point:

                continue

            if self.timeStampList[i] > endTime + point:

                break

            self.longSignalList.append(self.rawLongSignalList[i])
            self.shortSignalList.append(self.rawShortSignalList[i])





