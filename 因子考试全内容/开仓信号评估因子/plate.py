point = 0.00001
import numpy as np

def getEffectivePrice(priceList):
    '''不需要重写'''
    '''
    :param priceList: 传入十档价格列表
    :return:
    '''

    i = 1
    while i<= len(priceList):
        if priceList[-i] > point:
            return priceList[-i]
        i+=1
    return priceList[0]

def getAskPriceLimit(askPrice,lastAskPrice,width):
    '''不需要重写'''
    '''可以理解为当前和下一条tick卖一价中较高的加上width,正常是取不到卖十价位的'''

    maxAsk = getEffectivePrice(askPrice)
    maxLastAsk = getEffectivePrice(lastAskPrice)
    '''
    max(askPrice[0],lastAskPrice[0])+width,当前和上一条tick卖一价中较高的价格加上width,和两个卖十价中，取较小的值
    '''

    return min(maxAsk,maxLastAsk,max(askPrice[0],lastAskPrice[0])+width)

def getBidPriceLimit(bidPrice,lastBidPrice,width):
    '''不需要重写'''

    minBid = getEffectivePrice(bidPrice)
    minLastBid =getEffectivePrice(lastBidPrice)

    return max(minBid, minLastBid, min(bidPrice[0], lastBidPrice[0]) - width)

def volumeBelowPrice(price,askPrice,askVolume):
    '''不需要重写'''
    '''

    :param price: 限制价格，
    :param askPrice: 十档卖价，
    :param askVolume: 十档卖量
    :return:在限制范围内价格对应的卖量的和（单位:手数）
    '''

    volumeBelow = 0
    for i in range(0,len(askPrice)):

        if askPrice[i] < price + point and askPrice[i] > point:

            volumeBelow += askVolume[i]

    return volumeBelow

def volumeUpPrice(price,bidPrice,bidVolume):
    '''不需要重写'''

    volumeUp = 0
    for i in range(0, len(bidPrice)):

        if bidPrice[i] > price - point and bidPrice[i] > point:
            volumeUp += bidVolume[i]

    return volumeUp

def threeSecondMovement(lastAskPrice,lastBidPrice,lastAskVolume,lastBidVolume,askPrice,bidPrice,askVolume,bidVolume,width):
    '''不需要重写'''
    '''
    前一条tick的卖十档价位列表，卖、买十档价，卖量，买量，的列表
    当前tick的卖十档价位列表，卖、买十档价，卖量，买量，的列表
    每次取当前tick和上一条tick
    '''
    askPriceLimit =getAskPriceLimit(askPrice, lastAskPrice, width)
    '''
    当前tick的卖十档价位，前一条tick的卖十档价位，width
    返回较高卖一价加width,
    '''
    bidPriceLimit = getBidPriceLimit(bidPrice,lastBidPrice,width)
    '''
    当前tick的买十档价位，前一条tick的买十档价位，width
    返回较低卖一价减width,
    '''

    lastVolumeBelow = volumeBelowPrice(askPriceLimit,lastAskPrice,lastAskVolume)#前一条价格限制范围内tick卖量的和
    '''
    example
    卖一价15.2，width 0.03
    askPriceLimit，15.23,
    当天tick卖十档价位，当天tick卖十档量

    '''
    volumeBelow = volumeBelowPrice(askPriceLimit,askPrice,askVolume)#当前tick价格限制范围内tick卖量的和

    lastVolumeUp = volumeUpPrice(bidPriceLimit,lastBidPrice,lastBidVolume)#前一条价格限制范围内tick买量的和
    volumeUp = volumeUpPrice(bidPriceLimit,bidPrice,bidVolume)#当前tick价格限制范围内tick买量的和

    deltaAsk = lastVolumeBelow - volumeBelow#当前tick区间内卖量和减少值
    deltaBid = volumeUp - lastVolumeUp#当前tick区间买量和增加值

    return deltaAsk + deltaBid

def longResist(askPrice,askVolume,width):
    '''不需要重写'''
    '''

    :param askPrice:
    :param askVolume:
    :param width:
    :return: 卖价和卖一价距离在width之内，盘口手数叠加
    '''

    resist = 0

    for i in range(0,10):

        if askPrice[i] < askPrice[0] + width + point and askPrice[i] >point:
            '''
            example
            卖一价15，width 0.02 15.020000001
            卖二价，卖三价，，，，在区间之内的，价格

            '''

            resist += askVolume[i]

    return resist

def shortResist(bidPrice,bidVolume,width):
    '''不需要重写'''
    '''

    :param bidPrice:
    :param bidVolume:
    :param width:
    :return: 买价和买一价距离在width之内，盘口手数叠加
    '''

    resist = 0
    for i in range(0, 10):

        if bidPrice[i] > bidPrice[0] - width - point:
            '''
            example
            买一价15，width 0.02 14.079999999999
            买二价，买三价，，，，在区间之内的价格

            '''
            resist += bidVolume[i]

    return resist

class plateResearch:

    def __init__(self,askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList,platePercent):

        self.askPriceList = askPriceList
        self.bidPriceList = bidPriceList
        self.askVolumeList = askVolumeList
        self.bidVolumeList = bidVolumeList
        self.volumeList = volumeList
        self.amountList = amountList
        self.lastPriceList = lastPriceList
        self.timeStampList = timeStampList

        self.platePercent = platePercent
        self.width = 0

        self.longResistList = []
        self.shortResistList = []

        self.movementList = [0]

        self.longMovementSignalList = []
        self.shortMovementSignalList = []

        self.longPlateSizeSignalList = []
        self.shortPlateSizeSignalList = []

        self.longPlateSignalList =[]
        self.shortPlateSignalList = []

    def getRationalWidth(self):
        '''不需要重写'''
        '''
        price,第一条tick的买一，卖一价的平均价
        width,0.01百分之一，0.1，百分之十
        :return:
        '''

        price = (self.askPriceList[0][0] + self.bidPriceList[0][0]) * 0.5
        self.width = np.ceil(price * self.platePercent / 0.01) * 0.01

    def getResist(self):
        '''不需要重写'''
        '''每条tick对应的有效买卖价格范围内的成交量之和，即有效成交量，有效范围由自己给定的参数判定'''

        for i in range(0, len(self.askPriceList)):
            #此tick的有效卖量和
            self.longResistList.append(longResist(self.askPriceList[i], self.askVolumeList[i], self.width))
            # 此tick的有效买量和
            self.shortResistList.append(shortResist(self.bidPriceList[i], self.bidVolumeList[i], self.width))


    def getMovement(self):
        '''不需要重写'''

        for i in range(1, len(self.askPriceList)):
            '''
            第二条tick的卖十档价位列表，卖、买十档价，卖量，买量，的列表
            第一条tick的卖十档价位列表，卖、买十档价，卖量，买量，的列表
            每次取当前tick和上一条tick
            '''

            movement = threeSecondMovement(self.askPriceList[i - 1], self.bidPriceList[i - 1], self.askVolumeList[i - 1], self.bidVolumeList[i - 1],
                                self.askPriceList[i], self.bidPriceList[i], self.askVolumeList[i], self.bidVolumeList[i],self.width)
            if movement > point:
                '''
                example
                卖量减少150，当前为60，买量增加50
                movementlist (150+50)/60
                列表为百分比组成
                '''
                self.movementList.append(movement/self.longResistList[i])
            elif movement < - point:
                '''
               example
               卖量1000，买量2000
               movementlist (1000-2000)/2000
               列表为负的百分比
                '''
                self.movementList.append(movement / self.shortResistList[i])
            else:
                self.movementList.append(0)

    def getMovementSignal(self,ratioLowMovement,ratioHighMovement):
        '''不需要重写'''
        '''

        :param ratioLow: 4
        :param ratioHigh: 2000
        :return: 当前tick动量比例超过4，则赋值为1，否则为0。小于-4赋值为1，否则为0.两个并行列表
        '''

        for i in range(0, len(self.movementList)):

            if self.movementList[i] >= ratioLowMovement and self.movementList[i] <= ratioHighMovement :

                self.longMovementSignalList.append(1)
            else:
                self.longMovementSignalList.append(0)

            if -self.movementList[i] >= ratioLowMovement and -self.movementList[i] <=ratioHighMovement :
                self.shortMovementSignalList.append(1)
            else:
                self.shortMovementSignalList.append(0)

    def getPlateSizeSignal(self,ratioLowSize,ratioHighSize):
        '''不需要重写'''
        '''从每条tick的有效卖量和列表，有效买量和列表，根据数据关系判断盘口单量多头信号列表，空头信号列表
        self.shortResistList 为tick有效买量组成的列表
        self.longPlateSizeSignalList 为多信号列表
        '''


        for i in range(0, len(self.shortResistList)):

            if self.shortResistList[i] >= self.longResistList[i] * ratioLowSize and self.shortResistList[i] <= self.longResistList[i] * ratioHighSize:

                self.longPlateSizeSignalList.append(1)

            else:
                self.longPlateSizeSignalList.append(0)

            if self.longResistList[i] >= self.shortResistList[i] * ratioLowSize and self.longResistList[i] <= self.shortResistList[i] * ratioHighSize:

                self.shortPlateSizeSignalList.append(1)

            else:
                self.shortPlateSizeSignalList.append(0)

    def getCombineSignal(self):

        for i in range(0, len(self.longMovementSignalList)):

            if self.longMovementSignalList[i] ==1 and  self.longPlateSizeSignalList[i]==1:

                self.longPlateSignalList.append(1)
            else:
                self.longPlateSignalList.append(0)

            if self.shortMovementSignalList[i] == 1 and self.shortPlateSizeSignalList[i] == 1:

                self.shortPlateSignalList.append(1)
            else:
                self.shortPlateSignalList.append(0)