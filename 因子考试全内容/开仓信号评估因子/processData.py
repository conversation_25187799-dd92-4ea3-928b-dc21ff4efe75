point = 0.000001
import pandas as pd
import filedata


def processStockData(dataList,startTime,endTime):

    askPriceList = []
    bidPriceList = []
    lastPriceList = []
    volumeList=[]
    amountList = []
    askVolumeList = []
    bidVolumeList = []
    timeStampList = []

    for i in range(0, len(dataList)):

        time = dataList[i][4] / 1000.0

        if time > startTime and time < endTime:

            lastPrice = dataList[i][10] / 10000.0
            if abs(lastPrice) < point:
                continue
            askPrice = []
            askVolume = []
            bidPrice = []
            bidVolume = []
            for j in range(0,10):
                askPrice.append(dataList[i][11+j]/10000.0)
                askVolume.append(dataList[i][21+j]/100.0)
                bidPrice.append(dataList[i][31+j]/10000.0)
                bidVolume.append(dataList[i][41+j]/100.0)


            if i == 0:
                volume =dataList[i][52] / 100.0
                amount =dataList[i][53] / 100.0
            else:
                volume =dataList[i][52] / 100.0 - dataList[i - 1][52] / 100.0
                amount =dataList[i][53] / 100.0 - dataList[i - 1][53] / 100.0

            if askPrice[0] == 0.0 or bidPrice[0] == 0.0:
                break

            timeStampList.append(time)
            askPriceList.append(askPrice)
            bidPriceList.append(bidPrice)
            askVolumeList.append(askVolume)
            bidVolumeList.append(bidVolume)
            lastPriceList.append(lastPrice)
            volumeList.append(volume)
            amountList.append(amount)

    return askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList

if __name__ == '__main__':

    stockCode = "300274.SZ"
    stockData = pd.read_csv(filedata.path + str(20190610) + f'_{stockCode}.csv').values
    startTime = 93000
    endTime = 103000
    askPriceList,bidPriceList,askVolumeList,bidVolumeList,volumeList,amountList,lastPriceList,timeStampList=processStockData(stockData, startTime, endTime)

    for i in range(200,800):
        print(askPriceList[i],bidPriceList[i],askVolumeList[i],bidVolumeList[i],volumeList[i],amountList[i],lastPriceList[i],timeStampList[i])