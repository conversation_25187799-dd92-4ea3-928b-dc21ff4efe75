import datetime

def dateToDateTime(date):

    strDate = str(date)
    year = int(strDate[0:4])
    month = int(strDate[4:6])
    day = int(strDate[6:])

    return datetime.date(year,month,day)

def dateTimeToDate(datetime):
    year = datetime.year
    month = datetime.month
    day = datetime.day

    if len(str(day)) ==2:
        strDay = str(day)
    if len(str(day)) ==1:
        strDay = "0"+str(day)

    if len(str(month)) ==2:
        strMonth = str(month)
    if len(str(month)) ==1:
        strMonth = "0"+str(month)

    strDate = str(year) + strMonth+ strDay
    return strDate

def getDateList(startDate,endDate):

    dateList = []
    datetimeStart = dateToDateTime(startDate)
    datetimeEnd = dateToDateTime(endDate)

    date = datetimeStart
    oneDay = datetime.timedelta(days=1)

    while date <= datetimeEnd:
        dateList.append(dateTimeToDate(date))
        date += oneDay

    return dateList

if __name__ == '__main__':

    startDate ="20200815"
    endDate = "20211115"
    print(getDateList(startDate,endDate))