# -*- coding: utf-8 -*-
import pandas
import multiprocessing
from tqdm import tqdm
import singleStockSpaceTest as ssst
point = 0.000001
processNumber = 48


def getStockList():

    dataFrame = pandas.read_csv("stockList_YU.csv", encoding='gbk')
    stockCodeList = []
    stockNameList = []

    for i in range(0,len(dataFrame)):

        stockCodeList.append(dataFrame.iloc[i,0])
        stockNameList.append(dataFrame.iloc[i,1])

    return stockCodeList,stockNameList

def basketStockTest(startDate,endDate,startTime, endTime,spaceHigh,spaceLow, percent):
    stockCodeList,stockNameList = getStockList()
    ret_list = []
    pbar = tqdm(total=len(stockCodeList), desc='{} calc para'.format(pandas.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')))
    pool = multiprocessing.Pool(min(multiprocessing.cpu_count(), len(stockCodeList), processNumber))
    for i in range(len(stockCodeList)):
        ret_list.append(pool.apply_async(ssst.getSingleStockResult, args=(
        startDate,endDate,stockCodeList[i],startTime, endTime,percent,spaceLow,spaceHigh),
                                         callback=lambda x: pbar.update()))

    pool.close()
    pool.join()
    pbar.close()

    ret_list = list(map(lambda x: x.get(), ret_list))
    longMovementWinRateList = [g[0] for g in ret_list]
    lengthLongList = [g[1] for g in ret_list]
    shortMovementWinRateList = [g[2] for g in ret_list]
    lengthShortList = [g[3] for g in ret_list]

    #输出所有的股票的加权平均统计结果
    movementLong = 0
    movementShort = 0
    for i in range(0,len(longMovementWinRateList)):
        movementLong += longMovementWinRateList[i] *lengthLongList[i]
        movementShort += shortMovementWinRateList[i] * lengthShortList[i]
    print(movementLong/sum(lengthLongList),movementShort/sum(lengthShortList))
    print(sum(lengthLongList),sum(lengthShortList))


    result = pandas.DataFrame({"股票代码": stockCodeList, "股票名称":stockNameList,
                           "多头动量胜率":longMovementWinRateList,"多头样本量":lengthLongList,
                               "空头动量胜率": shortMovementWinRateList, "空头样本量": lengthShortList,
                           },
                          index=None)
    str = 'space.csv'
    result.to_csv(str, encoding='gbk', )

if __name__ == '__main__':

    #统计的开始结束时间
    startTime = 93100
    endTime = 95000

    #统计的开始结束后期
    startDate = 20220701
    endDate = 20220801

    #空间因子的上下限区间，闭区间
    spaceHigh = 20000.0
    spaceLow = 5.0

    #空间K线的宽度
    percent = 0.001

    basketStockTest(startDate, endDate, startTime, endTime, spaceHigh, spaceLow, percent)