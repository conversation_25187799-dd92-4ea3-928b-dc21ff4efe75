# -*- coding: utf-8 -*-
import processData as pd
import getDateList as dl
import space as sp
import numpy as np

def getDailyResult(date, stockCode,startTime, endTime,percent,spaceLow,spaceHigh):
    stockData = pd.getData(date, stockCode)
    askPriceList, bidPriceList, askVolumeList, bidVolumeList, volumeList, amountList, lastPriceList, timeStampList = pd.processStockData(
        stockData, startTime, endTime)
    width = np.ceil(lastPriceList[0] * percent/0.01)*0.01
    spaceList = sp.getSpaceList(askPriceList,bidPriceList,width)
    longMovement, longRevert, shortMovement, shortRevert = sp.MovementOrRevert(spaceList,spaceLow,spaceHigh)

    return longMovement, longRevert, shortMovement, shortRevert

def getSingleStockResult(startDate,endDate,stockCode,startTime, endTime,percent,spaceLow,spaceHigh):

    dateList = dl.getDateList(startDate, endDate)
    totalMovementLong = 0
    totalRevertLong = 0
    totalMovementShort = 0
    totalRevertShort = 0
    for date in dateList:
        try:
            movementLong, revertLong, movementShort, revertShort = getDailyResult(date, stockCode,startTime, endTime,percent,spaceLow,spaceHigh )
        except:
            continue
        else:

            totalMovementLong += movementLong
            totalRevertLong += revertLong
            totalMovementShort += movementShort
            totalRevertShort += revertShort

    lengthLong = totalMovementLong+totalRevertLong
    if lengthLong == 0:
        longMovementWinRate=0.5
    else:
        longMovementWinRate = totalMovementLong/lengthLong

    lengthShort = totalMovementShort + totalRevertShort
    if lengthShort == 0:
        shortMovementWinRate = 0.5
    else:
        shortMovementWinRate = totalMovementShort / lengthShort

    return longMovementWinRate,lengthLong,shortMovementWinRate,lengthShort

if __name__ == '__main__':

    #股票代码
    stockCode = "300750.SZ"

    # 统计的开始结束时间
    startTime = 93100
    endTime = 145000

    # 统计的开始结束后期
    startDate = 20220701
    endDate = 20220725

    # 空间因子的上下限区间，闭区间
    spaceHigh = 20000.0
    spaceLow = 5.0

    # 空间K线的宽度
    percent = 0.001

    print(getSingleStockResult(startDate, endDate, stockCode, startTime, endTime, percent, spaceLow, spaceHigh))